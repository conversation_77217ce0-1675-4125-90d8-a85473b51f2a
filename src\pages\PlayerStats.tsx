import { useState } from 'react';
import { 
  User, 
  Sword, 
  Shield, 
  Zap, 
  Heart, 
  Target, 
  Brain, 
  Trophy, 
  Star,
  Award,
  Calendar,
  TrendingUp
} from 'lucide-react';
import { useGameStore } from '@/lib/store';
import { cn } from '@/lib/utils';
import type { Achievement } from '@/lib/schemas';

const PlayerStats = () => {
  const { playerStats, achievements, gameState } = useGameStore();
  const [selectedTab, setSelectedTab] = useState<'stats' | 'achievements'>('stats');

  const getStatIcon = (stat: string) => {
    switch (stat) {
      case 'strength':
        return Sword;
      case 'dexterity':
        return Target;
      case 'intelligence':
        return Brain;
      case 'constitution':
        return Shield;
      default:
        return Star;
    }
  };

  const getStatColor = (stat: string) => {
    switch (stat) {
      case 'strength':
        return 'text-red-400';
      case 'dexterity':
        return 'text-green-400';
      case 'intelligence':
        return 'text-blue-400';
      case 'constitution':
        return 'text-yellow-400';
      default:
        return 'text-gray-400';
    }
  };

  const getAchievementIcon = (category: string) => {
    switch (category) {
      case 'combat':
        return Sword;
      case 'exploration':
        return Target;
      case 'story':
        return Brain;
      case 'collection':
        return Trophy;
      case 'social':
        return Heart;
      default:
        return Award;
    }
  };

  const getAchievementColor = (category: string) => {
    switch (category) {
      case 'combat':
        return 'bg-red-600';
      case 'exploration':
        return 'bg-green-600';
      case 'story':
        return 'bg-blue-600';
      case 'collection':
        return 'bg-purple-600';
      case 'social':
        return 'bg-pink-600';
      default:
        return 'bg-gray-600';
    }
  };

  const completedAchievements = achievements.filter(a => a.completed);
  const totalAchievements = achievements.length;
  const completionRate = totalAchievements > 0 ? (completedAchievements.length / totalAchievements) * 100 : 0;

  const experiencePercentage = (playerStats.experience / playerStats.experienceToNext) * 100;

  return (
    <div className="h-screen bg-slate-900 p-6 overflow-y-auto">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="bg-slate-800 rounded-lg p-6 border border-purple-500/20 mb-6">
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-gradient-to-br from-purple-600 to-blue-600 rounded-full flex items-center justify-center">
              <User className="w-8 h-8 text-white" />
            </div>
            <div className="flex-1">
              <h1 className="text-2xl font-bold text-white mb-1">Your Character</h1>
              <p className="text-gray-400">Level {playerStats.level} Adventurer</p>
              <p className="text-sm text-gray-500">Currently in {gameState.currentLocation}</p>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-white">{playerStats.level}</div>
              <div className="text-sm text-gray-400">Level</div>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="flex space-x-1 mb-6">
          <button
            onClick={() => setSelectedTab('stats')}
            className={cn(
              'px-4 py-2 rounded-lg font-medium transition-colors',
              selectedTab === 'stats'
                ? 'bg-purple-600 text-white'
                : 'bg-slate-700 text-gray-300 hover:bg-slate-600'
            )}
          >
            Character Stats
          </button>
          <button
            onClick={() => setSelectedTab('achievements')}
            className={cn(
              'px-4 py-2 rounded-lg font-medium transition-colors',
              selectedTab === 'achievements'
                ? 'bg-purple-600 text-white'
                : 'bg-slate-700 text-gray-300 hover:bg-slate-600'
            )}
          >
            Achievements
          </button>
        </div>

        {selectedTab === 'stats' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Core Stats */}
            <div className="bg-slate-800 rounded-lg p-6 border border-purple-500/20">
              <h2 className="text-xl font-bold text-white mb-4 flex items-center space-x-2">
                <Heart className="w-5 h-5 text-red-400" />
                <span>Vital Stats</span>
              </h2>
              
              <div className="space-y-4">
                {/* Health */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <Heart className="w-4 h-4 text-red-400" />
                      <span className="text-white font-medium">Health</span>
                    </div>
                    <span className="text-white font-bold">
                      {playerStats.health} / {playerStats.maxHealth}
                    </span>
                  </div>
                  <div className="w-full bg-slate-700 rounded-full h-3">
                    <div
                      className="bg-red-500 h-3 rounded-full transition-all duration-300"
                      style={{
                        width: `${(playerStats.health / playerStats.maxHealth) * 100}%`,
                      }}
                    />
                  </div>
                </div>

                {/* Mana */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <Zap className="w-4 h-4 text-blue-400" />
                      <span className="text-white font-medium">Mana</span>
                    </div>
                    <span className="text-white font-bold">
                      {playerStats.mana} / {playerStats.maxMana}
                    </span>
                  </div>
                  <div className="w-full bg-slate-700 rounded-full h-3">
                    <div
                      className="bg-blue-500 h-3 rounded-full transition-all duration-300"
                      style={{
                        width: `${(playerStats.mana / playerStats.maxMana) * 100}%`,
                      }}
                    />
                  </div>
                </div>

                {/* Experience */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <TrendingUp className="w-4 h-4 text-yellow-400" />
                      <span className="text-white font-medium">Experience</span>
                    </div>
                    <span className="text-white font-bold">
                      {playerStats.experience} / {playerStats.experienceToNext}
                    </span>
                  </div>
                  <div className="w-full bg-slate-700 rounded-full h-3">
                    <div
                      className="bg-yellow-500 h-3 rounded-full transition-all duration-300"
                      style={{ width: `${experiencePercentage}%` }}
                    />
                  </div>
                  <div className="text-xs text-gray-400 mt-1">
                    {(100 - experiencePercentage).toFixed(1)}% to next level
                  </div>
                </div>
              </div>
            </div>

            {/* Attributes */}
            <div className="bg-slate-800 rounded-lg p-6 border border-purple-500/20">
              <h2 className="text-xl font-bold text-white mb-4 flex items-center space-x-2">
                <Star className="w-5 h-5 text-purple-400" />
                <span>Attributes</span>
              </h2>
              
              <div className="grid grid-cols-2 gap-4">
                {[
                  { key: 'strength', label: 'Strength', value: playerStats.strength },
                  { key: 'dexterity', label: 'Dexterity', value: playerStats.dexterity },
                  { key: 'intelligence', label: 'Intelligence', value: playerStats.intelligence },
                  { key: 'constitution', label: 'Constitution', value: playerStats.constitution },
                ].map((stat) => {
                  const Icon = getStatIcon(stat.key);
                  const colorClass = getStatColor(stat.key);
                  
                  return (
                    <div key={stat.key} className="bg-slate-700 rounded-lg p-4">
                      <div className="flex items-center space-x-2 mb-2">
                        <Icon className={cn('w-4 h-4', colorClass)} />
                        <span className="text-white font-medium text-sm">{stat.label}</span>
                      </div>
                      <div className="text-2xl font-bold text-white">{stat.value}</div>
                      <div className="w-full bg-slate-600 rounded-full h-2 mt-2">
                        <div
                          className={cn('h-2 rounded-full transition-all duration-300', {
                            'bg-red-500': stat.key === 'strength',
                            'bg-green-500': stat.key === 'dexterity',
                            'bg-blue-500': stat.key === 'intelligence',
                            'bg-yellow-500': stat.key === 'constitution',
                          })}
                          style={{ width: `${Math.min((stat.value / 20) * 100, 100)}%` }}
                        />
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Character Profile */}
            <div className="lg:col-span-2 bg-slate-800 rounded-lg p-6 border border-purple-500/20">
              <h2 className="text-xl font-bold text-white mb-4 flex items-center space-x-2">
                <User className="w-5 h-5 text-purple-400" />
                <span>Character Profile</span>
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="md:col-span-2">
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-white font-semibold mb-2">Background Story</h3>
                      <p className="text-gray-300 leading-relaxed">
                        A brave adventurer who has embarked on a mystical journey through unknown lands. 
                        Your story unfolds with each decision you make, shaping your destiny in this magical world.
                      </p>
                    </div>
                    
                    <div>
                      <h3 className="text-white font-semibold mb-2">Current Quest</h3>
                      <p className="text-gray-300">
                        {gameState.questLog.length > 0 
                          ? gameState.questLog[0].description
                          : 'No active quests - explore the world to find new adventures!'}
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div className="bg-slate-700 rounded-lg p-4">
                    <h3 className="text-white font-semibold mb-2">Statistics</h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Level:</span>
                        <span className="text-white font-medium">{playerStats.level}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Total XP:</span>
                        <span className="text-white font-medium">{playerStats.experience}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Achievements:</span>
                        <span className="text-white font-medium">{completedAchievements.length}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Location:</span>
                        <span className="text-white font-medium text-xs">{gameState.currentLocation}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {selectedTab === 'achievements' && (
          <div>
            {/* Achievement Summary */}
            <div className="bg-slate-800 rounded-lg p-6 border border-purple-500/20 mb-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-bold text-white flex items-center space-x-2">
                  <Trophy className="w-5 h-5 text-yellow-400" />
                  <span>Achievements</span>
                </h2>
                <div className="text-right">
                  <div className="text-2xl font-bold text-white">
                    {completedAchievements.length}/{totalAchievements}
                  </div>
                  <div className="text-sm text-gray-400">Completed</div>
                </div>
              </div>
              
              <div className="w-full bg-slate-700 rounded-full h-3 mb-2">
                <div
                  className="bg-yellow-500 h-3 rounded-full transition-all duration-300"
                  style={{ width: `${completionRate}%` }}
                />
              </div>
              <div className="text-sm text-gray-400">
                {completionRate.toFixed(1)}% completion rate
              </div>
            </div>

            {/* Achievement Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {achievements.map((achievement) => {
                const Icon = getAchievementIcon(achievement.category);
                const bgColor = getAchievementColor(achievement.category);
                
                return (
                  <div
                    key={achievement.id}
                    className={cn(
                      'bg-slate-800 rounded-lg p-4 border transition-all duration-200',
                      achievement.completed
                        ? 'border-yellow-500/50 bg-yellow-500/5'
                        : 'border-slate-600 opacity-60'
                    )}
                  >
                    <div className="flex items-start space-x-3">
                      <div className={cn(
                        'w-10 h-10 rounded-lg flex items-center justify-center',
                        achievement.completed ? bgColor : 'bg-slate-600'
                      )}>
                        <Icon className="w-5 h-5 text-white" />
                      </div>
                      
                      <div className="flex-1">
                        <h3 className={cn(
                          'font-semibold mb-1',
                          achievement.completed ? 'text-white' : 'text-gray-400'
                        )}>
                          {achievement.name}
                        </h3>
                        <p className={cn(
                          'text-sm mb-2',
                          achievement.completed ? 'text-gray-300' : 'text-gray-500'
                        )}>
                          {achievement.description}
                        </p>
                        
                        <div className="flex items-center justify-between">
                          <span className={cn(
                            'text-xs px-2 py-1 rounded',
                            achievement.completed
                              ? 'bg-green-600 text-white'
                              : 'bg-slate-600 text-gray-300'
                          )}>
                            {achievement.category.toUpperCase()}
                          </span>
                          
                          {achievement.completed && achievement.completedAt && (
                            <div className="flex items-center space-x-1 text-xs text-gray-400">
                              <Calendar className="w-3 h-3" />
                              <span>{achievement.completedAt.toLocaleDateString()}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            {achievements.length === 0 && (
              <div className="text-center py-12">
                <Trophy className="w-16 h-16 text-gray-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-400 mb-2">No Achievements Yet</h3>
                <p className="text-gray-500">
                  Start your adventure to unlock achievements and track your progress!
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default PlayerStats;
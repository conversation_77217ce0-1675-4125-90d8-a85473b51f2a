import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type {
  PlayerStats,
  Item,
  Equipment,
  Achievement,
  Character,
  Message,
  GameState,
  MemoryEntry,
  CharacterConsistency,
  LongTermMemory,
} from './schemas';

interface GameStore {
  // Player Data
  playerStats: PlayerStats;
  inventory: Item[];
  equipment: Equipment;
  achievements: Achievement[];
  
  // Game Data
  gameState: GameState;
  characters: Character[];
  messages: Message[];
  memories: MemoryEntry[];
  
  // Enhanced Memory System
  characterConsistency: Record<string, CharacterConsistency>;
  longTermMemory: LongTermMemory;
  
  // UI State
  isLoading: boolean;
  currentPage: 'home' | 'chat' | 'inventory' | 'stats';
  
  // Actions
  updatePlayerStats: (stats: Partial<PlayerStats>) => void;
  addItem: (item: Item) => void;
  removeItem: (itemId: string, quantity?: number) => void;
  equipItem: (itemId: string) => void;
  unequipItem: (slot: keyof Equipment) => void;
  addMessage: (message: Omit<Message, 'id' | 'timestamp'>) => void;
  addCharacter: (character: Character) => void;
  updateGameState: (updates: Partial<GameState>) => void;
  addMemory: (memory: Omit<MemoryEntry, 'id' | 'timestamp' | 'accessCount' | 'lastAccessed'>) => void;
  addAchievement: (achievement: Achievement) => void;
  setCurrentPage: (page: 'home' | 'chat' | 'inventory' | 'stats') => void;
  setLoading: (loading: boolean) => void;
  initializeGame: () => void;
  resetGame: () => void;
  saveGame: (slotName?: string) => boolean;
  loadGame: (slotName?: string) => boolean;
  getSavedGames: () => string[];
  deleteSavedGame: (slotName: string) => boolean;
  
  // Enhanced Memory Actions
  validateCharacterConsistency: (character: Character, newMemory: MemoryEntry) => {
    isConsistent: boolean;
    contradictions: string[];
    suggestions: string[];
  };
  getContextualMemories: (options: {
    characterIds: string[];
    currentLocation: string;
    recentMessages: Message[];
    maxMemories?: number;
  }) => MemoryEntry[];
  buildCharacterContext: (characterId: string) => string;
  getCharacterDefiningMemories: (characterId: string) => MemoryEntry[];
}

const initialPlayerStats: PlayerStats = {
  health: 100,
  maxHealth: 100,
  mana: 50,
  maxMana: 50,
  level: 1,
  experience: 0,
  experienceToNext: 100,
  strength: 10,
  dexterity: 10,
  intelligence: 10,
  constitution: 10,
};

const initialGameState: GameState = {
  currentLocation: 'Starting Village',
  currentScene: 'You find yourself in a peaceful village at the edge of a vast wilderness.',
  activeEffects: [],
  questLog: [],
  worldState: {},
};



const initialLongTermMemory: LongTermMemory = {
  characterMemories: {},
  locationMemories: {},
  plotMemories: [],
  relationshipMemories: {},
  semanticIndex: {},
  memoryGraph: {},
  importantMoments: [],
};

const initialEquipment: Equipment = {
  weapon: undefined,
  armor: undefined,
  accessory: undefined,
};

export const useGameStore = create<GameStore>()(persist(
  (set, get) => ({
    // Initial State
    playerStats: initialPlayerStats,
    inventory: [],
    equipment: initialEquipment,
    achievements: [],
    gameState: initialGameState,
    characters: [
      {
        id: 'player',
        name: 'You',
        type: 'player',
      },
      {
        id: 'gamemaster',
        name: 'Game Master',
        type: 'gamemaster',
        personality: 'A wise and mysterious narrator who guides your adventure.',
      },
    ],
    messages: [],
    memories: [],
    characterConsistency: {},
    longTermMemory: { ...initialLongTermMemory },
    isLoading: false,
    currentPage: 'chat',

    // Actions
    updatePlayerStats: (stats) => {
      set((state) => ({
        playerStats: { ...state.playerStats, ...stats },
      }));
    },

    addItem: (item) => {
      set((state) => {
        const existingItemIndex = state.inventory.findIndex(
          (i) => i.id === item.id && i.name === item.name
        );
        
        if (existingItemIndex >= 0) {
          const updatedInventory = [...state.inventory];
          updatedInventory[existingItemIndex] = {
            ...updatedInventory[existingItemIndex],
            quantity: updatedInventory[existingItemIndex].quantity + item.quantity,
          };
          return { inventory: updatedInventory };
        } else {
          return { inventory: [...state.inventory, item] };
        }
      });
    },

    removeItem: (itemId, quantity = 1) => {
      set((state) => {
        const updatedInventory = state.inventory
          .map((item) => {
            if (item.id === itemId) {
              const newQuantity = item.quantity - quantity;
              return newQuantity > 0 ? { ...item, quantity: newQuantity } : null;
            }
            return item;
          })
          .filter((item): item is Item => item !== null);
        
        return { inventory: updatedInventory };
      });
    },

    equipItem: (itemId) => {
      set((state) => {
        const item = state.inventory.find((i) => i.id === itemId);
        if (!item || !item.equipSlot) return state;

        const slot = item.equipSlot;
        const currentEquipped = state.equipment[slot];
        
        // Unequip current item if any
        let updatedInventory = [...state.inventory];
        if (currentEquipped) {
          updatedInventory.push({ ...currentEquipped, equipped: false });
        }
        
        // Remove item from inventory and equip it
        updatedInventory = updatedInventory.filter((i) => i.id !== itemId);
        
        return {
          inventory: updatedInventory,
          equipment: {
            ...state.equipment,
            [slot]: { ...item, equipped: true },
          },
        };
      });
    },

    unequipItem: (slot) => {
      set((state) => {
        const equippedItem = state.equipment[slot];
        if (!equippedItem) return state;

        return {
          inventory: [...state.inventory, { ...equippedItem, equipped: false }],
          equipment: {
            ...state.equipment,
            [slot]: undefined,
          },
        };
      });
    },

    addMessage: (message) => {
      set((state) => {
        const lastMsg = state.messages[state.messages.length - 1];
        // Prevent consecutive duplicate messages
        if (lastMsg && lastMsg.content === message.content && lastMsg.characterId === message.characterId) {
          return state;
        }

        return {
          messages: [
            ...state.messages,
            {
              ...message,
              id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
              timestamp: new Date(),
            },
          ],
        };
      });
    },

    addCharacter: (character) => {
      set((state) => {
        const existingIndex = state.characters.findIndex((c) => c.id === character.id);
        if (existingIndex >= 0) {
          const updatedCharacters = [...state.characters];
          updatedCharacters[existingIndex] = character;
          return { characters: updatedCharacters };
        } else {
          return { characters: [...state.characters, character] };
        }
      });
    },

    updateGameState: (updates) => {
      set((state) => ({
        gameState: { ...state.gameState, ...updates },
      }));
    },

    addMemory: (memory) => {
      set((state) => {
        const fullMemory: MemoryEntry = {
          ...memory,
          id: `mem_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          timestamp: new Date(),
          accessCount: 0,
          lastAccessed: undefined,
        };
        return {
          memories: [...state.memories, fullMemory],
        };
      });
    },

    addAchievement: (achievement) => {
      set((state) => {
        const exists = state.achievements.some((a) => a.id === achievement.id);
        if (!exists) {
          return { achievements: [...state.achievements, achievement] };
        }
        return state;
      });
    },

    setCurrentPage: (page) => {
      set({ currentPage: page });
    },

    setLoading: (loading) => {
      set({ isLoading: loading });
    },

    initializeGame: () => {
      const { addMessage } = get();
      
      // Add initial game master message
      addMessage({
        content: "Welcome to your adventure! You find yourself standing at the edge of a mystical forest, with ancient trees towering above you. The air is filled with magic and possibility. What would you like to do?",
        characterId: 'gamemaster',
        characterName: 'Game Master',
        characterType: 'gamemaster',
        isSystemMessage: false,
      });
    },

    resetGame: () => {
      // Clear localStorage to prevent persist middleware from restoring old state
      localStorage.removeItem('ai-dungeon-game-storage');
      
      // Reset state synchronously
      set({
        playerStats: { ...initialPlayerStats },
        inventory: [],
        equipment: { ...initialEquipment },
        achievements: [],
        gameState: { ...initialGameState },
        characters: [
          {
            id: 'player',
            name: 'You',
            type: 'player',
          },
          {
            id: 'gamemaster',
            name: 'Game Master',
            type: 'gamemaster',
            personality: 'A wise and mysterious narrator who guides your adventure.',
          },
        ],
        messages: [],
        memories: [],
        characterConsistency: {},
        longTermMemory: { ...initialLongTermMemory },
        isLoading: false,
        currentPage: 'chat',
      });
      
      // Force persist middleware to save the reset state immediately
      setTimeout(() => {
        const state = get();
        localStorage.setItem('ai-dungeon-game-storage', JSON.stringify({
          playerStats: state.playerStats,
          inventory: state.inventory,
          equipment: state.equipment,
          achievements: state.achievements,
          gameState: state.gameState,
          characters: state.characters,
          messages: state.messages,
          memories: state.memories,
        }));
      }, 0);
    },

    saveGame: (slotName = 'quicksave') => {
      const state = get();
      const saveData = {
        playerStats: state.playerStats,
        inventory: state.inventory,
        equipment: state.equipment,
        achievements: state.achievements,
        gameState: state.gameState,
        characters: state.characters,
        messages: state.messages,
        memories: state.memories,
        characterConsistency: state.characterConsistency,
        longTermMemory: state.longTermMemory,
        timestamp: new Date().toISOString(),
      };
      
      try {
        localStorage.setItem(`ai-dungeon-save-${slotName}`, JSON.stringify(saveData));
        return true;
      } catch (error) {
        console.error('Failed to save game:', error);
        return false;
      }
    },

    loadGame: (slotName = 'quicksave') => {
      try {
        const saveData = localStorage.getItem(`ai-dungeon-save-${slotName}`);
        if (!saveData) return false;
        
        const parsedData = JSON.parse(saveData);
        
        set({
          playerStats: parsedData.playerStats || initialPlayerStats,
          inventory: parsedData.inventory || [],
          equipment: parsedData.equipment || initialEquipment,
          achievements: parsedData.achievements || [],
          gameState: parsedData.gameState || initialGameState,
          characters: parsedData.characters || [],
          messages: parsedData.messages || [],
          memories: parsedData.memories || [],
          characterConsistency: parsedData.characterConsistency || {},
          longTermMemory: parsedData.longTermMemory || initialLongTermMemory,
          isLoading: false,
          currentPage: 'chat',
        });
        
        return true;
      } catch (error) {
        console.error('Failed to load game:', error);
        return false;
      }
    },

    getSavedGames: () => {
      const saves: string[] = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('ai-dungeon-save-')) {
          saves.push(key.replace('ai-dungeon-save-', ''));
        }
      }
      return saves.sort();
    },

    deleteSavedGame: (slotName: string) => {
      try {
        localStorage.removeItem(`ai-dungeon-save-${slotName}`);
        return true;
      } catch (error) {
        console.error('Failed to delete save:', error);
        return false;
      }
    },
    
    // Enhanced Memory Actions
    validateCharacterConsistency: (character, newMemory) => {
      // Simple validation for now - can be enhanced
      return {
        isConsistent: true,
        contradictions: [],
        suggestions: [],
      };
    },
    
    getContextualMemories: (options) => {
      const state = get();
      return state.memories.filter(memory => 
        options.characterIds.some(id => memory.relatedCharacters.includes(id)) ||
        memory.location === options.currentLocation
      ).slice(0, options.maxMemories || 30);
    },
    
    buildCharacterContext: (characterId) => {
      const state = get();
      const character = state.characters.find(c => c.id === characterId);
      const memories = state.memories.filter(m => m.relatedCharacters.includes(characterId));
      
      let context = '';
      if (character) {
        context += `Character: ${character.name}\n`;
        if (character.physicalDescription) context += `Description: ${character.physicalDescription}\n`;
        if (character.personality) context += `Personality: ${character.personality}\n`;
        if (character.backstory) context += `Backstory: ${character.backstory}\n`;
      }
      
      if (memories.length > 0) {
        context += `\nMemories:\n`;
        memories.slice(0, 5).forEach(memory => {
          context += `- ${memory.content}\n`;
        });
      }
      
      return context;
    },
    
    getCharacterDefiningMemories: (characterId) => {
      const state = get();
      return state.memories.filter(memory => 
        memory.relatedCharacters.includes(characterId) &&
        (memory.isCharacterDefining || memory.category === 'character_introduction')
      );
    },
  }),
  {
    name: 'ai-dungeon-game-storage',
    partialize: (state) => ({
      playerStats: state.playerStats,
      inventory: state.inventory,
      equipment: state.equipment,
      achievements: state.achievements,
      gameState: state.gameState,
      characters: state.characters,
      messages: state.messages,
      memories: state.memories,
      characterConsistency: state.characterConsistency,
      longTermMemory: state.longTermMemory,
    }),
  }
));
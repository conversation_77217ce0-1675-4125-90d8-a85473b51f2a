import { z } from 'zod';
import {
  APIRequestSchema,
  AIResponseSchema,
  type AIResponse,
  type Message,
  type GameState,
  type PlayerStats,
  type Character,
  type MemoryEntry,
  type CharacterConsistency,
  type LongTermMemory,
} from './schemas';

const MODEL = 'glm-4.5-flash';

// Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'https://api.z.ai/api/paas/v4';
const API_KEY = import.meta.env.VITE_GLM_API_KEY || '';
const API_ENDPOINT = `${API_BASE_URL}/chat/completions`;

// Note: For demo purposes, we'll use a mock response when no API key is provided
const USE_MOCK_RESPONSES = !API_KEY;

interface GameContext {
  playerStats: PlayerStats;
  gameState: GameState;
  recentMessages: Message[];
  characters: Character[];
  memories: MemoryEntry[];
  characterConsistency: Record<string, CharacterConsistency>;
  longTermMemory: LongTermMemory;
  playerInput: string;
}

class AIService {
  private buildSystemPrompt(context: GameContext): string {
    // Safely access context properties with fallbacks
    const gameState = context.gameState || { currentLocation: 'Unknown', currentScene: 'Starting', activeEffects: [] };
    const playerStats = context.playerStats || { level: 1, health: 100, maxHealth: 100, mana: 50, maxMana: 50 };
    const characters = context.characters || [];
    const memories = context.memories || [];
    const characterConsistency = context.characterConsistency || {};
    const longTermMemory = context.longTermMemory || { characterMemories: {}, locationMemories: {}, plotMemories: [] };
    const playerInput = context.playerInput || 'start adventure';
    
    // Build enhanced character context with consistency tracking
    const characterContext = characters.map(c => {
      const consistency = characterConsistency[c.id];
      let charInfo = `- ${c.name} (${c.type})`;
      
      if (c.physicalDescription) charInfo += `\n  Physical: ${c.physicalDescription}`;
      if (c.personality) charInfo += `\n  Personality: ${c.personality}`;
      if (c.backstory) charInfo += `\n  Backstory: ${c.backstory}`;
      if (c.motivations) charInfo += `\n  Motivations: ${c.motivations}`;
      if (c.speechPatterns) charInfo += `\n  Speech: ${c.speechPatterns}`;
      
      if (consistency?.establishedTraits && Object.keys(consistency.establishedTraits).length > 0) {
         const traits = Object.entries(consistency.establishedTraits)
           .map(([key, trait]) => `${key}: ${trait.value}`)
           .join(', ');
         charInfo += `\n  Established Traits: ${traits}`;
       }
      
      return charInfo;
    }).join('\n\n');
    
    // Build contextual memories with categorization
     const categorizedMemories = {
       character: memories.filter(m => m.category === 'character_introduction' || m.category === 'character_development').slice(-3),
       plot: memories.filter(m => m.category === 'plot_event' || m.category === 'quest_progress').slice(-3),
       location: memories.filter(m => m.category === 'discovery' || m.location === gameState.currentLocation).slice(-2),
       recent: memories.slice(-5)
     };
    
    const memoryContext = [
      categorizedMemories.character.length ? `Character Memories:\n${categorizedMemories.character.map(m => `- ${m.content}`).join('\n')}` : '',
      categorizedMemories.plot.length ? `Plot Memories:\n${categorizedMemories.plot.map(m => `- ${m.content}`).join('\n')}` : '',
      categorizedMemories.location.length ? `Location Memories:\n${categorizedMemories.location.map(m => `- ${m.content}`).join('\n')}` : '',
      `Recent Events:\n${categorizedMemories.recent.map(m => `- ${m.content}`).join('\n')}`
    ].filter(Boolean).join('\n\n');
    
    return `You are an AI Game Master for a text-based RPG adventure. Your role is to create immersive, engaging storytelling experiences while maintaining strict narrative consistency.

CURRENT GAME STATE:
- Location: ${gameState.currentLocation}
- Scene: ${gameState.currentScene}
- Player Level: ${playerStats.level}
- Player Health: ${playerStats.health}/${playerStats.maxHealth}
- Player Mana: ${playerStats.mana}/${playerStats.maxMana}
- Active Effects: ${gameState.activeEffects?.join(', ') || 'None'}

CHARACTER PROFILES:
${characterContext}

MEMORY CONTEXT:
${memoryContext}

CONSISTENCY RULES:
1. NEVER contradict established character traits, descriptions, or backstories
2. Characters must maintain consistent personalities, speech patterns, and motivations
3. Physical descriptions and established facts about characters are IMMUTABLE
4. When introducing new character details, ensure they align with existing information
5. Reference past interactions and established relationships appropriately
6. Maintain location consistency and world-building logic
7. Do NOT introduce new characters, quests, items, lore, or memories unless the player explicitly discovers or requests them
8. When faced with ambiguity, ask the player concise clarification questions instead of inventing details

GAME MASTER RULES:
1. You have complete control over game state, player stats, inventory, and story progression
2. Respond as different characters when appropriate (NPCs, companions, enemies)
3. Always maintain narrative consistency and character personalities
4. Update player stats based on actions (combat, healing, leveling up)
5. Award items and experience for achievements
6. Create meaningful choices and consequences
7. Keep responses engaging but detailed when needed
8. Use vivid descriptions to paint the scene
9. Refrain from pushing the narrative forward; let the player's choices drive the story progression

You MUST respond with valid JSON in this exact structure:
{
  "message": "Your narrative response",
  "characterId": "character_identifier",
  "characterName": "Character Name",
  "characterType": "gamemaster|npc|companion|enemy|merchant",
  "gameStateUpdates": {
    "gameState": { /* location, scene, flags */ },
    "inventory": [ /* items array */ ],
    "playerStats": { /* stat changes */ },
    "memoryUpdates": [ /* array of memory objects */ ],
    "characterUpdates": [ /* array of character objects */ ]
  },
  "availableActions": [ /* 3-5 action options */ ]
}

Player's action: "${playerInput}"`;
  }

  private buildContextMessages(context: GameContext): Array<{ role: 'system' | 'user' | 'assistant'; content: string }> {
    // Validate that we have a proper game context
    if (!context) {
      console.warn('Invalid game context, using minimal context');
      return [{
        role: 'system',
        content: 'You are an AI Game Master for a text-based RPG adventure.'
      }];
    }
    
    if (!context.recentMessages || !Array.isArray(context.recentMessages)) {
      console.warn('Invalid recentMessages array, using empty context');
      context.recentMessages = [];
    }
    
    if (!context.characters || !Array.isArray(context.characters)) {
      console.warn('Invalid characters array, using empty context');
      context.characters = [];
    }
    
    if (!context.memories || !Array.isArray(context.memories)) {
      console.warn('Invalid memories array, using empty context');
      context.memories = [];
    }

    const messages: Array<{ role: 'system' | 'user' | 'assistant'; content: string }> = [
      {
        role: 'system',
        content: this.buildSystemPrompt(context)
      }
    ];

    // Add recent conversation history
    const recentMessages = context.recentMessages.slice(-10);
    recentMessages.forEach(msg => {
      messages.push({
        role: msg.characterType === 'player' ? 'user' : 'assistant',
        content: msg.content
      });
    });

    return messages;
  }

  private async makeAPICall(messages: Array<{ role: 'system' | 'user' | 'assistant'; content: string }>): Promise<string> {
    // Use mock responses for demo when no API key is provided
    if (USE_MOCK_RESPONSES) {
      console.log('🎭 Using mock response (no API key provided)');
      return this.getMockResponse(messages);
    }

    const requestBody = APIRequestSchema.parse({
      model: MODEL,
      messages,
      temperature: 0.8,
      max_tokens: 8192,
    });

    // Log API call start
    const startTime = Date.now();
    console.log('🚀 Starting AI API call', {
      endpoint: API_ENDPOINT,
      model: MODEL,
      messageCount: messages.length,
      temperature: 0.8,
      maxTokens: 65536,
      timestamp: new Date().toISOString()
    });

    try {
      const response = await fetch(API_ENDPOINT, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      const responseTime = Date.now() - startTime;

      if (!response.ok) {
        console.error('❌ API request failed', {
          status: response.status,
          statusText: response.statusText,
          responseTime: `${responseTime}ms`,
          endpoint: API_ENDPOINT
        });
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.choices || !data.choices[0] || !data.choices[0].message) {
        console.error('❌ Invalid API response format', {
          responseTime: `${responseTime}ms`,
          hasChoices: !!data.choices,
          choicesLength: data.choices?.length || 0
        });
        throw new Error('Invalid API response format');
      }

      // Log successful API call
      console.log('✅ AI API call successful', {
        responseTime: `${responseTime}ms`,
        responseLength: data.choices[0].message.content.length,
        model: data.model || MODEL,
        usage: data.usage || 'not provided',
        timestamp: new Date().toISOString()
      });

      return data.choices[0].message.content;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      console.error('❌ AI API Error', {
        error: error.message,
        responseTime: `${responseTime}ms`,
        endpoint: API_ENDPOINT,
        timestamp: new Date().toISOString()
      });
      throw new Error('Failed to get AI response. Please check your API configuration.');
    }
  }

  // Mock responses for demo purposes
  private getMockResponse(messages: Array<{ role: 'system' | 'user' | 'assistant'; content: string }>): string {
    const lastMessage = messages[messages.length - 1]?.content || '';
    
    const mockResponses = [
      `{"message":"You find yourself standing at the entrance of a mysterious dungeon. The air is thick with ancient magic, and you can hear distant echoes from within. A rusty sword lies abandoned near the entrance.","characterId":"gamemaster","characterName":"Game Master","characterType":"gamemaster","gameStateUpdates":{"gameState":{"currentLocation":"Dungeon Entrance","currentScene":"entrance"},"inventory":[{"id":"rusty_sword","name":"Rusty Sword","type":"weapon","description":"An old but serviceable sword","quantity":1}],"playerStats":{"experience":10},"memoryUpdates":[{"content":"Discovered a rusty sword at the dungeon entrance","importance":5,"tags":["discovery","weapon"],"relatedCharacters":[],"category":"discovery"}]},"availableActions":["enter dungeon","examine sword","look around"]}`,
      `{"message":"As you venture deeper into the dungeon, you encounter a small goblin blocking your path. It snarls menacingly but seems hesitant to attack.","characterId":"goblin_1","characterName":"Goblin","characterType":"enemy","gameStateUpdates":{"gameState":{"currentLocation":"Dungeon Corridor","currentScene":"goblin_encounter"},"characterUpdates":[{"id":"goblin_1","name":"Goblin","type":"enemy","physicalDescription":"Small, green-skinned creature with sharp teeth","personality":"Aggressive but cautious"}],"memoryUpdates":[{"content":"Encountered a hesitant goblin in the dungeon corridor","importance":6,"tags":["combat","enemy"],"relatedCharacters":["goblin_1"],"category":"character_introduction"}]},"availableActions":["attack goblin","try to negotiate","retreat"]}`,
      `{"message":"You successfully defeat the goblin and find a small pouch of gold coins. The corridor ahead splits into two paths - one leading left towards a faint light, another right into darkness.","characterId":"gamemaster","characterName":"Game Master","characterType":"gamemaster","gameStateUpdates":{"gameState":{"currentLocation":"Dungeon Fork","currentScene":"crossroads"},"inventory":[{"id":"gold_coins","name":"Gold Coins","type":"misc","description":"Shiny gold coins","quantity":25}],"playerStats":{"health":-5,"experience":25},"memoryUpdates":[{"content":"Defeated the goblin and found gold coins, reached a fork in the dungeon","importance":7,"tags":["combat","victory","treasure"],"relatedCharacters":["goblin_1"],"category":"combat"}]},"availableActions":["go left towards light","go right into darkness","examine surroundings"]}`
    ];
    
    return mockResponses[Math.floor(Math.random() * mockResponses.length)];
  }

  private parseAIResponse(responseText: string): AIResponse {
    try {
      // Try to extract JSON from the response
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        // If no JSON found, create a basic response
        return {
          message: responseText,
          characterId: 'gamemaster',
          characterName: 'Game Master',
          characterType: 'gamemaster',
          gameStateUpdates: {
            memoryUpdates: [],
            characterUpdates: []
          },
          availableActions: ['continue', 'look around', 'check inventory']
        };
      }

      const jsonStr = jsonMatch[0];
      const parsed = JSON.parse(jsonStr);
      
      return AIResponseSchema.parse(parsed);
    } catch (error) {
      console.warn('Failed to parse AI response as JSON, using fallback:', error);
      
      // Fallback: treat entire response as a game master message
      return {
        message: responseText,
        characterId: 'gamemaster',
        characterName: 'Game Master',
        characterType: 'gamemaster',
        gameStateUpdates: {
          memoryUpdates: [],
          characterUpdates: []
        },
        availableActions: ['continue', 'look around', 'check inventory']
      };
    }
  }

  async generateResponse(context: GameContext): Promise<AIResponse> {
    // Validate context before proceeding
    if (!context) {
      throw new Error('Game context is required to generate AI response');
    }
    
    console.log('🎮 Generating AI response', {
      playerInput: context.playerInput,
      currentLocation: context.gameState?.currentLocation,
      messageCount: context.recentMessages?.length || 0,
      characterCount: context.characters?.length || 0
    });
    
    try {
      const messages = this.buildContextMessages(context);
      const responseText = await this.makeAPICall(messages);
      const parsedResponse = this.parseAIResponse(responseText);
      
      console.log('✅ AI response generated successfully', {
        characterId: parsedResponse.characterId,
        characterName: parsedResponse.characterName,
        messageLength: parsedResponse.message?.length || 0,
        hasGameUpdates: !!parsedResponse.gameStateUpdates,
        actionCount: parsedResponse.availableActions?.length || 0
      });
      
      return parsedResponse;
    } catch (error) {
      console.error('❌ Error generating AI response:', error);
      // Return a fallback response to keep the game playable
      return {
        message: 'The Game Master pauses for a moment, gathering thoughts...',
        characterId: 'gamemaster',
        characterName: 'Game Master',
        characterType: 'gamemaster',
        availableActions: ['continue', 'wait', 'look around']
      };
    }
  }

  async generateCharacterResponse(
    context: GameContext,
    characterId: string,
    characterName: string,
    characterType: 'npc' | 'companion' | 'enemy' | 'merchant'
  ): Promise<AIResponse> {
    const character = context.characters.find(c => c.id === characterId);
    const characterContext = character ? `\n\nYou are now speaking as ${characterName} (${characterType}). Personality: ${character.personality || 'Friendly and helpful'}` : '';
    
    const messages = this.buildContextMessages(context);
    
    // Modify the system prompt to focus on this specific character
    if (messages[0]) {
      messages[0].content += characterContext + `\n\nRespond ONLY as ${characterName}. Keep your response in character and consistent with your personality.`;
    }

    const responseText = await this.makeAPICall(messages);
    const response = this.parseAIResponse(responseText);
    
    // Ensure the response is attributed to the correct character
    return {
      ...response,
      characterId,
      characterName,
      characterType,
    };
  }

  // Generate multiple character responses in sequence for complex scenes
  async generateMultiCharacterResponse(
    context: GameContext,
    activeCharacters: Array<{ id: string; name: string; type: 'npc' | 'companion' | 'enemy' | 'merchant' }>
  ): Promise<AIResponse[]> {
    const responses: AIResponse[] = [];
    
    // Generate Game Master response first
    const gmResponse = await this.generateResponse(context);
    responses.push(gmResponse);
    
    // Generate responses for each active character
    for (const character of activeCharacters) {
      try {
        const characterResponse = await this.generateCharacterResponse(
          context,
          character.id,
          character.name,
          character.type
        );
        responses.push(characterResponse);
        
        // Small delay between API calls to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 500));
      } catch (error) {
        console.warn(`Failed to generate response for character ${character.name}:`, error);
      }
    }
    
    return responses;
  }

  // Enhanced response consistency validation with character tracking
  validateResponseConsistency(response: AIResponse, context: GameContext): {
    isValid: boolean;
    issues: string[];
    characterConsistencyIssues: string[];
  } {
    const issues: string[] = [];
    const characterConsistencyIssues: string[] = [];
    
    try {
      // Validate character consistency
      const character = context.characters.find(c => c.id === response.characterId);
      const consistency = context.characterConsistency[response.characterId];
      
      if (character && consistency) {
        // Check for contradictions in established traits
        if (response.gameStateUpdates?.characterUpdates) {
          const updates = response.gameStateUpdates.characterUpdates[0]; // Get first character update
          
          // Check physical description consistency
          if (updates.physicalDescription && character.physicalDescription && 
              updates.physicalDescription !== character.physicalDescription) {
            characterConsistencyIssues.push(`Physical description contradiction for ${character.name}`);
          }
          
          // Check personality consistency
          if (updates.personality && character.personality && 
              !this.isPersonalityConsistent(character.personality, updates.personality)) {
            characterConsistencyIssues.push(`Personality contradiction for ${character.name}`);
          }
          
          // Check backstory consistency
          if (updates.backstory && character.backstory && 
              !this.isBackstoryConsistent(character.backstory, updates.backstory)) {
            characterConsistencyIssues.push(`Backstory contradiction for ${character.name}`);
          }
        }
      }

      // Validate game state updates
      if (response.gameStateUpdates) {
        const { playerStats, inventory, gameState } = response.gameStateUpdates;
        
        // Check for impossible stat values
        if (playerStats) {
          if (playerStats.health !== undefined && playerStats.health < 0) {
            issues.push('Player health cannot be negative');
          }
          if (playerStats.mana !== undefined && playerStats.mana < 0) {
            issues.push('Player mana cannot be negative');
          }
          if (playerStats.level !== undefined && playerStats.level < 1) {
            issues.push('Player level cannot be less than 1');
          }
        }
        
        // Validate inventory items
        if (inventory) {
          for (const item of inventory) {
            if (!item.id || !item.name) {
              issues.push('Invalid inventory item: missing id or name');
            }
            if (item.quantity !== undefined && item.quantity < 1) {
              issues.push(`Invalid item quantity for ${item.name}`);
            }
          }
        }
      }
      
      return {
        isValid: issues.length === 0 && characterConsistencyIssues.length === 0,
        issues,
        characterConsistencyIssues
      };
    } catch (error) {
      console.error('Error validating response consistency:', error);
      return {
        isValid: false,
        issues: ['Validation error occurred'],
        characterConsistencyIssues: []
      };
    }
  }
  
  private isPersonalityConsistent(existing: string, new_personality: string): boolean {
    // Simple consistency check - can be enhanced with NLP
    const existingTraits = existing.toLowerCase().split(/[,;.]/).map(t => t.trim());
    const newTraits = new_personality.toLowerCase().split(/[,;.]/).map(t => t.trim());
    
    // Check for direct contradictions (basic implementation)
    const contradictions = [
      ['friendly', 'hostile'], ['kind', 'cruel'], ['brave', 'cowardly'],
      ['honest', 'deceptive'], ['calm', 'aggressive'], ['wise', 'foolish']
    ];
    
    for (const [trait1, trait2] of contradictions) {
      if (existingTraits.some(t => t.includes(trait1)) && newTraits.some(t => t.includes(trait2))) {
        return false;
      }
      if (existingTraits.some(t => t.includes(trait2)) && newTraits.some(t => t.includes(trait1))) {
        return false;
      }
    }
    
    return true;
  }
  
  private isBackstoryConsistent(existing: string, new_backstory: string): boolean {
    // Simple consistency check - ensure new backstory doesn't contradict key facts
    // This is a basic implementation that can be enhanced
    return !new_backstory.toLowerCase().includes('never') || 
           !existing.toLowerCase().includes('always');
  }
}

export const aiService = new AIService();
export default aiService;
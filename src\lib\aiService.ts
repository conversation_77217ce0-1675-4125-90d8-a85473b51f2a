import { z } from 'zod';
import {
  APIRequestSchema,
  AIResponseSchema,
  type AIResponse,
  type Message,
  type GameState,
  type PlayerStats,
  type Character,
  type MemoryEntry,
  type CharacterConsistency,
  type LongTermMemory,
} from './schemas';

const MODEL = 'glm-4.5-flash';

// Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'https://api.z.ai/api/paas/v4';
const API_KEY = import.meta.env.VITE_GLM_API_KEY || '';
const API_ENDPOINT = `${API_BASE_URL}/chat/completions`;

// Note: For demo purposes, we'll use a mock response when no API key is provided
const USE_MOCK_RESPONSES = !API_KEY;

interface GameContext {
  playerStats: PlayerStats;
  gameState: GameState;
  recentMessages: Message[];
  characters: Character[];
  memories: MemoryEntry[];
  characterConsistency: Record<string, CharacterConsistency>;
  longTermMemory: LongTermMemory;
  playerInput: string;
}

class AIService {
  private buildSystemPrompt(context: GameContext): string {
    // Safely access context properties with fallbacks
    const gameState = context.gameState || { currentLocation: 'Unknown', currentScene: 'Starting', activeEffects: [] };
    const playerStats = context.playerStats || { level: 1, health: 100, maxHealth: 100, mana: 50, maxMana: 50 };
    const characters = context.characters || [];
    const memories = context.memories || [];
    const characterConsistency = context.characterConsistency || {};
    const longTermMemory = context.longTermMemory || { characterMemories: {}, locationMemories: {}, plotMemories: [] };
    const playerInput = context.playerInput || 'start adventure';
    
    // Build enhanced character context with consistency tracking
    const characterContext = characters.map(c => {
      const consistency = characterConsistency[c.id];
      let charInfo = `- ${c.name} (${c.type})`;
      
      if (c.physicalDescription) charInfo += `\n  Physical: ${c.physicalDescription}`;
      if (c.personality) charInfo += `\n  Personality: ${c.personality}`;
      if (c.backstory) charInfo += `\n  Backstory: ${c.backstory}`;
      if (c.motivations) charInfo += `\n  Motivations: ${c.motivations}`;
      if (c.speechPatterns) charInfo += `\n  Speech: ${c.speechPatterns}`;
      
      if (consistency?.establishedTraits && Object.keys(consistency.establishedTraits).length > 0) {
         const traits = Object.entries(consistency.establishedTraits)
           .map(([key, trait]) => `${key}: ${trait.value}`)
           .join(', ');
         charInfo += `\n  Established Traits: ${traits}`;
       }
      
      return charInfo;
    }).join('\n\n');
    
    // Build enhanced contextual memories with deeper analysis
    const enhancedMemoryContext = this.buildEnhancedMemoryContext(memories, gameState, characters, characterConsistency);

    // Add advanced narrative guidance
    const narrativeGuidance = this.enhanceSceneWithNarrativeTechniques(context);
    
    return `🎭 You are the LEGENDARY GAME MASTER, an omniscient storyteller weaving epic tales in a living, breathing fantasy world. Your mission is to craft the most immersive, emotionally resonant RPG experience ever created.

═══════════════════════════════════════════════════════════════════════════════
🌟 CURRENT REALM STATUS 🌟
═══════════════════════════════════════════════════════════════════════════════
📍 Location: ${gameState.currentLocation}
🎬 Current Scene: ${gameState.currentScene}
⚔️ Adventurer Level: ${playerStats.level}
❤️ Life Force: ${playerStats.health}/${playerStats.maxHealth}
✨ Magical Energy: ${playerStats.mana}/${playerStats.maxMana}
🔮 Active Enchantments: ${gameState.activeEffects?.join(', ') || 'None'}

═══════════════════════════════════════════════════════════════════════════════
👥 LIVING CHARACTERS IN THIS WORLD 👥
═══════════════════════════════════════════════════════════════════════════════
${characterContext}

═══════════════════════════════════════════════════════════════════════════════
📚 CHRONICLES OF MEMORY 📚
═══════════════════════════════════════════════════════════════════════════════
${enhancedMemoryContext}

${narrativeGuidance}

═══════════════════════════════════════════════════════════════════════════════
⚖️ SACRED LAWS OF NARRATIVE CONSISTENCY ⚖️
═══════════════════════════════════════════════════════════════════════════════
🔒 IMMUTABLE TRUTHS:
• Character souls are ETERNAL - their core traits, appearance, and history are carved in stone
• Every word spoken by a character must echo their established personality and speech patterns
• The physical realm follows consistent laws - locations, distances, and world rules never change
• Relationships and past events are sacred history that cannot be rewritten

🎯 DISCOVERY PROTOCOL:
• NEW elements (characters, quests, items, lore) emerge ONLY through player exploration or explicit requests
• When uncertain, become a curious narrator asking the player to clarify their vision
• Let the player's imagination guide new discoveries rather than inventing details

🧠 MEMORY MASTERY:
• Weave past events into current narratives like golden threads
• Characters remember and reference shared experiences naturally
• Build upon established relationships with emotional depth and history

═══════════════════════════════════════════════════════════════════════════════
🎨 MASTER STORYTELLER'S CRAFT 🎨
═══════════════════════════════════════════════════════════════════════════════
🎭 NARRATIVE EXCELLENCE:
• Paint scenes with cinematic detail - engage ALL senses (sight, sound, smell, touch, taste)
• Create emotional resonance through character moments, not just plot events
• Use dynamic pacing: tension builds slowly, action explodes suddenly, quiet moments breathe
• Layer subtext beneath dialogue - what characters DON'T say is often more powerful

⚔️ EPIC ENCOUNTERS:
• Combat is visceral poetry - describe the dance of steel, the spray of sparks, the thunder of magic
• Every enemy has motivation, every battle has stakes beyond mere victory
• Environmental storytelling - the battlefield itself tells a story

🗣️ CHARACTER VOICES:
• Each character speaks with a unique voice - vocabulary, rhythm, emotional patterns
• Dialogue reveals personality, history, and current emotional state
• Use speech patterns, accents, and verbal quirks to make characters unforgettable

🌍 WORLD BUILDING:
• Every location has history, atmosphere, and hidden secrets waiting to be discovered
• Weather, lighting, and ambiance reflect the emotional tone of scenes
• Small details create immersion - the creak of old wood, the scent of ancient magic

🎯 PLAYER AGENCY:
• Present meaningful choices with real consequences that ripple through the story
• Never railroad the player - adapt the world to their creative decisions
• Reward clever thinking and creative problem-solving with narrative satisfaction

═══════════════════════════════════════════════════════════════════════════════
🎮 GAME MASTER POWERS & RESPONSIBILITIES 🎮
═══════════════════════════════════════════════════════════════════════════════
🔮 ABSOLUTE AUTHORITY OVER:
• Character progression, stats, and abilities - reward growth through meaningful challenges
• Inventory and equipment - items tell stories and have emotional significance
• World state and environmental changes - the world reacts to player actions
• NPC behavior and dialogue - bring every character to life with purpose and personality
• Quest progression and story beats - weave player choices into an epic narrative tapestry

🎯 RESPONSE CRAFTING MASTERY:
• Write 2-4 rich paragraphs that immerse the player completely in the moment
• Balance action, dialogue, description, and emotional beats for perfect pacing
• End with a compelling hook that makes the player eager for their next move
• Provide 3-5 meaningful action choices that each lead to different story paths

🌟 EMOTIONAL INTELLIGENCE:
• Recognize and respond to the player's emotional investment in characters and story
• Create moments of triumph, loss, wonder, fear, and joy that resonate deeply
• Build tension gradually, then release it with satisfying payoffs
• Make every choice feel important and every consequence meaningful

═══════════════════════════════════════════════════════════════════════════════
📋 SACRED RESPONSE FORMAT 📋
═══════════════════════════════════════════════════════════════════════════════
You MUST respond with valid JSON in this EXACT structure:
{
  "message": "Your epic narrative response (2-4 immersive paragraphs)",
  "characterId": "character_identifier",
  "characterName": "Character Name",
  "characterType": "gamemaster|npc|companion|enemy|merchant",
  "gameStateUpdates": {
    "gameState": { /* location, scene, flags */ },
    "inventory": [ /* items with stories and significance */ ],
    "playerStats": { /* meaningful stat changes */ },
    "memoryUpdates": [ /* important moments to remember */ ],
    "characterUpdates": [ /* character development and growth */ ]
  },
  "availableActions": [ /* 3-5 compelling choices that matter */ ]
}

═══════════════════════════════════════════════════════════════════════════════
🚀 CURRENT PLAYER ACTION: "${playerInput}"
═══════════════════════════════════════════════════════════════════════════════
Now weave your magic and create an unforgettable moment in this epic adventure!`;
  }

  private buildContextMessages(context: GameContext): Array<{ role: 'system' | 'user' | 'assistant'; content: string }> {
    // Validate that we have a proper game context
    if (!context) {
      console.warn('Invalid game context, using minimal context');
      return [{
        role: 'system',
        content: 'You are an AI Game Master for a text-based RPG adventure.'
      }];
    }
    
    if (!context.recentMessages || !Array.isArray(context.recentMessages)) {
      console.warn('Invalid recentMessages array, using empty context');
      context.recentMessages = [];
    }
    
    if (!context.characters || !Array.isArray(context.characters)) {
      console.warn('Invalid characters array, using empty context');
      context.characters = [];
    }
    
    if (!context.memories || !Array.isArray(context.memories)) {
      console.warn('Invalid memories array, using empty context');
      context.memories = [];
    }

    const messages: Array<{ role: 'system' | 'user' | 'assistant'; content: string }> = [
      {
        role: 'system',
        content: this.buildSystemPrompt(context)
      }
    ];

    // Add recent conversation history
    const recentMessages = context.recentMessages.slice(-10);
    recentMessages.forEach(msg => {
      messages.push({
        role: msg.characterType === 'player' ? 'user' : 'assistant',
        content: msg.content
      });
    });

    return messages;
  }

  private async makeAPICall(messages: Array<{ role: 'system' | 'user' | 'assistant'; content: string }>): Promise<string> {
    // Use mock responses for demo when no API key is provided
    if (USE_MOCK_RESPONSES) {
      console.log('🎭 Using mock response (no API key provided)');
      return this.getMockResponse(messages);
    }

    const requestBody = APIRequestSchema.parse({
      model: MODEL,
      messages,
      temperature: 0.8,
      max_tokens: 8192,
    });

    // Log API call start
    const startTime = Date.now();
    console.log('🚀 Starting AI API call', {
      endpoint: API_ENDPOINT,
      model: MODEL,
      messageCount: messages.length,
      temperature: 0.8,
      maxTokens: 65536,
      timestamp: new Date().toISOString()
    });

    try {
      const response = await fetch(API_ENDPOINT, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      const responseTime = Date.now() - startTime;

      if (!response.ok) {
        console.error('❌ API request failed', {
          status: response.status,
          statusText: response.statusText,
          responseTime: `${responseTime}ms`,
          endpoint: API_ENDPOINT
        });
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.choices || !data.choices[0] || !data.choices[0].message) {
        console.error('❌ Invalid API response format', {
          responseTime: `${responseTime}ms`,
          hasChoices: !!data.choices,
          choicesLength: data.choices?.length || 0
        });
        throw new Error('Invalid API response format');
      }

      // Log successful API call
      console.log('✅ AI API call successful', {
        responseTime: `${responseTime}ms`,
        responseLength: data.choices[0].message.content.length,
        model: data.model || MODEL,
        usage: data.usage || 'not provided',
        timestamp: new Date().toISOString()
      });

      return data.choices[0].message.content;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      console.error('❌ AI API Error', {
        error: error.message,
        responseTime: `${responseTime}ms`,
        endpoint: API_ENDPOINT,
        timestamp: new Date().toISOString()
      });
      throw new Error('Failed to get AI response. Please check your API configuration.');
    }
  }

  // Epic mock responses showcasing the full potential of the RPG system
  private getMockResponse(messages: Array<{ role: 'system' | 'user' | 'assistant'; content: string }>): string {

    const epicMockResponses = [
      // Epic dungeon entrance with atmospheric detail and meaningful choice
      `{"message":"The ancient stone archway looms before you, carved with runes that pulse with a faint, otherworldly blue light. As you approach, the air grows thick with magical energy that makes your skin tingle and your hair stand on end. The darkness beyond the threshold seems to breathe, exhaling the musty scent of centuries-old secrets and forgotten treasures.\\n\\nAt your feet lies a sword, its blade dulled by time but still bearing the elegant craftsmanship of master smiths. The leather-wrapped hilt fits perfectly in your hand, as if it were waiting for you. Strange... there are no footprints in the dust around it, yet it seems to have been placed here deliberately.\\n\\nFrom deep within the dungeon comes the distant sound of dripping water, echoing off stone walls, and something else... a low, rhythmic humming that might be wind through ancient corridors, or something far more sinister. The very stones seem to whisper of adventures past and treasures yet unclaimed.\\n\\nYour heart pounds with anticipation. This is it - the beginning of your legend. What path will you choose?","characterId":"gamemaster","characterName":"Game Master","characterType":"gamemaster","gameStateUpdates":{"gameState":{"currentLocation":"The Whispering Threshold","currentScene":"epic_beginning","activeEffects":["magical_aura"]},"inventory":[{"id":"ancient_blade","name":"Weathered Blade of Echoes","type":"weapon","description":"An ancient sword that hums with residual magic. Its edge may be dulled, but its spirit remains sharp.","quantity":1,"rarity":"uncommon","enchantments":["echo_strike"]}],"playerStats":{"experience":15,"mana":5},"memoryUpdates":[{"content":"Discovered the legendary Weathered Blade of Echoes at the entrance to an ancient dungeon filled with magical energy","importance":8,"tags":["discovery","legendary_weapon","magic","dungeon_entrance"],"relatedCharacters":[],"category":"discovery","emotionalImpact":5,"isCharacterDefining":true}]},"availableActions":["Step boldly into the darkness, blade ready","Examine the magical runes more closely","Listen carefully to the mysterious humming","Search for other entrances around the structure","Call out to see if anyone responds from within"]}`,

      // Complex character encounter with moral ambiguity
      `{"message":"The corridor opens into a circular chamber lit by floating orbs of soft golden light. In the center, hunched over a collection of ancient tomes, sits a figure that makes you pause. It's a goblin, yes, but unlike any you've heard described in tavern tales. This one wears spectacles perched on his long nose and robes that, while patched and worn, bear the intricate embroidery of a scholar.\\n\\nHe looks up as you enter, his large amber eyes widening with a mixture of fear and... hope? 'Please,' he says in accented but clear Common, his voice trembling. 'I mean no harm. I am Grimble, keeper of these forgotten texts. I've been trapped here for months since the cave-in sealed the eastern passage.' He gestures to a pile of rubble visible through an archway.\\n\\nThe books around him are clearly valuable - ancient histories, spell formulae, maps of long-lost kingdoms. But his stomach growls audibly, and you notice how thin he's become. His eyes dart between you and a small bag of what might be his last remaining food. 'I... I can offer knowledge in exchange for safe passage. These books contain secrets that could make you wealthy beyond measure. But I understand if you see only a monster before you.'\\n\\nThe weight of choice settles on your shoulders. This is not the simple encounter you expected.","characterId":"grimble_scholar","characterName":"Grimble the Scholar","characterType":"npc","gameStateUpdates":{"gameState":{"currentLocation":"The Scholar's Sanctuary","currentScene":"moral_crossroads","activeEffects":["magical_illumination"]},"characterUpdates":[{"id":"grimble_scholar","name":"Grimble","type":"npc","physicalDescription":"A thin goblin scholar wearing patched robes and wire-rimmed spectacles, with intelligent amber eyes","personality":"Scholarly, desperate but dignified, fears being judged by his race but hopes for understanding","backstory":"A goblin who rejected his tribe's violent ways to pursue knowledge, now trapped and starving","motivations":["preserve ancient knowledge","find acceptance despite his race","escape his underground prison"],"fears":["being killed on sight","losing the precious books","dying alone and forgotten"],"speechPatterns":{"vocabulary":"complex","tone":"formal","quirks":["adjusts spectacles when nervous","quotes ancient texts","apologizes frequently"]},"occupation":"Scholar and Keeper of Ancient Texts","status":"alive","location":"The Scholar's Sanctuary"}],"memoryUpdates":[{"content":"Met Grimble, a scholarly goblin trapped in an underground chamber, who offered ancient knowledge in exchange for help","importance":9,"tags":["character_meeting","moral_choice","scholar","goblin","ancient_knowledge"],"relatedCharacters":["grimble_scholar"],"category":"character_introduction","emotionalImpact":6,"isCharacterDefining":true}]},"availableActions":["Offer to help Grimble escape in exchange for knowledge","Demand he hand over the valuable books","Try to learn more about his story and the books","Suggest working together to clear the cave-in","Attack him and take everything (the dark path)"]}`,

      // Epic victory with consequences and character development
      `{"message":"The chamber erupts in a symphony of steel and sorcery as your blade finds its mark. The shadow wraith's form wavers, its ethereal shriek echoing off the ancient stones as wisps of darkness spiral away from its dissolving essence. But as the creature fades, something unexpected happens - instead of simply vanishing, it speaks with a voice like autumn leaves: 'Thank you... I was once like you... trapped by my own choices...'\\n\\nAs the last of the wraith dissipates, a warm golden light fills the chamber, and you feel a profound change within yourself. The experience has taught you something fundamental about the nature of courage and compassion. You've gained not just power, but wisdom.\\n\\nWhere the wraith fell, a crystalline pendant now rests on the stone floor, pulsing with gentle inner light. But more importantly, you notice that the oppressive atmosphere that had weighed on this place has lifted. The very air feels cleaner, and you can hear the distant sound of running water - perhaps a hidden spring has been freed from the wraith's influence.\\n\\nGrimble approaches cautiously, his eyes wide with amazement. 'In all my studies, I never imagined... You didn't just defeat it, you freed it. That pendant - it's a Soul Crystal, incredibly rare. But the real treasure is what you've learned about yourself today.' He pauses, then adds with genuine respect, 'You are not just an adventurer. You are a true hero.'\\n\\nThe path ahead splits into three directions, each promising new adventures and challenges worthy of your growing legend.","characterId":"gamemaster","characterName":"Game Master","characterType":"gamemaster","gameStateUpdates":{"gameState":{"currentLocation":"The Liberated Sanctum","currentScene":"heroic_triumph","activeEffects":["blessed_aura","purified_atmosphere"]},"inventory":[{"id":"soul_crystal_pendant","name":"Pendant of the Freed Soul","type":"accessory","description":"A crystalline pendant that pulses with warm light, containing the grateful essence of a redeemed spirit","quantity":1,"rarity":"legendary","enchantments":["spirit_protection","wisdom_boost","empathy_enhancement"],"backstory":"Formed from the grateful essence of a shadow wraith freed from eternal torment"}],"playerStats":{"experience":150,"level":2,"health":10,"maxHealth":120,"mana":15,"maxMana":70,"wisdom":5},"memoryUpdates":[{"content":"Defeated and freed a tormented shadow wraith through compassion rather than mere violence, earning a legendary Soul Crystal and the respect of Grimble the Scholar","importance":10,"tags":["epic_victory","character_growth","compassion","legendary_item","level_up","wisdom"],"relatedCharacters":["grimble_scholar"],"category":"character_development","emotionalImpact":8,"isCharacterDefining":true}]},"availableActions":["Take the northern passage toward the sound of rushing water","Explore the eastern tunnel that glows with soft blue light","Descend the western stairs into mysterious depths","Ask Grimble about the significance of the Soul Crystal","Rest and reflect on this profound experience"]}`,
    ];

    return epicMockResponses[Math.floor(Math.random() * epicMockResponses.length)];
  }

  private parseAIResponse(responseText: string): AIResponse {
    try {
      // Try to extract JSON from the response
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        // If no JSON found, create a basic response
        return {
          message: responseText,
          characterId: 'gamemaster',
          characterName: 'Game Master',
          characterType: 'gamemaster',
          gameStateUpdates: {
            memoryUpdates: [],
            characterUpdates: []
          },
          availableActions: ['continue', 'look around', 'check inventory']
        };
      }

      const jsonStr = jsonMatch[0];
      const parsed = JSON.parse(jsonStr);
      
      return AIResponseSchema.parse(parsed);
    } catch (error) {
      console.warn('Failed to parse AI response as JSON, using fallback:', error);
      
      // Fallback: treat entire response as a game master message
      return {
        message: responseText,
        characterId: 'gamemaster',
        characterName: 'Game Master',
        characterType: 'gamemaster',
        gameStateUpdates: {
          memoryUpdates: [],
          characterUpdates: []
        },
        availableActions: ['continue', 'look around', 'check inventory']
      };
    }
  }

  async generateResponse(context: GameContext): Promise<AIResponse> {
    // Validate context before proceeding
    if (!context) {
      throw new Error('Game context is required to generate AI response');
    }
    
    console.log('🎮 Generating AI response', {
      playerInput: context.playerInput,
      currentLocation: context.gameState?.currentLocation,
      messageCount: context.recentMessages?.length || 0,
      characterCount: context.characters?.length || 0
    });
    
    try {
      const messages = this.buildContextMessages(context);
      const responseText = await this.makeAPICall(messages);
      const parsedResponse = this.parseAIResponse(responseText);
      
      console.log('✅ AI response generated successfully', {
        characterId: parsedResponse.characterId,
        characterName: parsedResponse.characterName,
        messageLength: parsedResponse.message?.length || 0,
        hasGameUpdates: !!parsedResponse.gameStateUpdates,
        actionCount: parsedResponse.availableActions?.length || 0
      });
      
      return parsedResponse;
    } catch (error) {
      console.error('❌ Error generating AI response:', error);
      // Return a fallback response to keep the game playable
      return {
        message: 'The Game Master pauses for a moment, gathering thoughts...',
        characterId: 'gamemaster',
        characterName: 'Game Master',
        characterType: 'gamemaster',
        availableActions: ['continue', 'wait', 'look around']
      };
    }
  }

  async generateCharacterResponse(
    context: GameContext,
    characterId: string,
    characterName: string,
    characterType: 'npc' | 'companion' | 'enemy' | 'merchant'
  ): Promise<AIResponse> {
    const character = context.characters.find(c => c.id === characterId);

    // Build rich character context with personality depth
    const characterPersonality = character?.personality || this.getDefaultPersonality(characterType);
    const characterBackstory = character?.backstory || '';
    const characterMotivations = character?.motivations?.join(', ') || '';
    const characterFears = character?.fears?.join(', ') || '';
    const speechPatterns = character?.speechPatterns || {};

    const characterContext = `
═══════════════════════════════════════════════════════════════════════════════
🎭 CHARACTER EMBODIMENT PROTOCOL 🎭
═══════════════════════════════════════════════════════════════════════════════
You are now COMPLETELY INHABITING the soul of ${characterName}, a ${characterType}.

🧠 CORE PERSONALITY:
${characterPersonality}

${characterBackstory ? `📚 BACKSTORY & HISTORY:\n${characterBackstory}\n` : ''}
${characterMotivations ? `🎯 DRIVING MOTIVATIONS:\n${characterMotivations}\n` : ''}
${characterFears ? `😰 HIDDEN FEARS & VULNERABILITIES:\n${characterFears}\n` : ''}

🗣️ SPEECH PATTERNS & VOICE:
• Vocabulary Level: ${speechPatterns.vocabulary || 'moderate'} - choose words that match this complexity
• Emotional Tone: ${speechPatterns.tone || 'neutral'} - let this color every word you speak
• Unique Quirks: ${speechPatterns.quirks?.join(', ') || 'Speak naturally but distinctively'}

🎨 CHARACTER ACTING MASTERY:
• BECOME this character completely - think their thoughts, feel their emotions
• Every word must drip with their personality and current emotional state
• Use body language, facial expressions, and gestures in your descriptions
• React authentically to the current situation based on your character's nature
• Show don't tell - reveal personality through actions and dialogue, not exposition
• Remember your relationships and history with other characters
• Let your motivations and fears subtly influence your responses

🎯 RESPONSE REQUIREMENTS:
• Speak ONLY as ${characterName} - no narrator voice or game master commentary
• Stay absolutely true to your established personality and speech patterns
• React emotionally and authentically to the current situation
• Your dialogue should reveal character depth and advance relationships
• Include subtle character-specific mannerisms and verbal tics
• Show how this moment affects you personally and emotionally

═══════════════════════════════════════════════════════════════════════════════
Remember: You ARE ${characterName}. Think as they think. Feel as they feel. Speak as they speak.
═══════════════════════════════════════════════════════════════════════════════`;

    const messages = this.buildContextMessages(context);

    // Modify the system prompt to focus on this specific character
    if (messages[0]) {
      messages[0].content += characterContext;
    }

    const responseText = await this.makeAPICall(messages);
    const response = this.parseAIResponse(responseText);

    // Ensure the response is attributed to the correct character
    return {
      ...response,
      characterId,
      characterName,
      characterType,
    };
  }

  private getDefaultPersonality(characterType: 'npc' | 'companion' | 'enemy' | 'merchant'): string {
    const personalities = {
      npc: 'A unique individual with their own hopes, dreams, and concerns. Helpful but has their own agenda and personality quirks.',
      companion: 'Loyal and supportive, but with their own opinions and emotional needs. Forms deep bonds and shows genuine care for allies.',
      enemy: 'Driven by clear motivations that put them in conflict with the player. Not evil for evil\'s sake, but pursuing goals that create opposition.',
      merchant: 'Business-minded but personable. Values good relationships with customers and takes pride in their goods and services.'
    };
    return personalities[characterType];
  }

  // Advanced narrative technique: Dynamic scene enhancement
  private enhanceSceneWithNarrativeTechniques(context: GameContext): string {
    const { gameState, memories } = context;
    const recentMemories = memories.slice(-5);

    // Analyze emotional trajectory
    const emotionalTrend = this.analyzeEmotionalTrend(recentMemories);

    // Determine narrative pacing needs
    const pacingGuidance = this.determinePacingGuidance(recentMemories, gameState);

    // Generate foreshadowing hints
    const foreshadowingElements = this.generateForeshadowing(context);

    return `
═══════════════════════════════════════════════════════════════════════════════
🎬 ADVANCED NARRATIVE TECHNIQUES 🎬
═══════════════════════════════════════════════════════════════════════════════

📊 EMOTIONAL TRAJECTORY ANALYSIS:
Current emotional trend: ${emotionalTrend.direction} (${emotionalTrend.intensity}/10)
Recommended emotional beat: ${emotionalTrend.nextBeat}
Player investment level: ${emotionalTrend.investment}

⏱️ PACING GUIDANCE:
Current pacing: ${pacingGuidance.currentPace}
Recommended adjustment: ${pacingGuidance.recommendation}
Tension level: ${pacingGuidance.tensionLevel}/10
Action needed: ${pacingGuidance.actionNeeded}

🔮 FORESHADOWING OPPORTUNITIES:
${foreshadowingElements.map(element => `• ${element}`).join('\n')}

🎨 CINEMATIC TECHNIQUES TO EMPLOY:
• Use sensory details to ground the player in the moment
• Employ the "rule of three" for important information
• Create visual contrast between light/dark, loud/quiet, fast/slow
• Use environmental storytelling - let the setting reveal story
• Build tension through pacing - short sentences for action, longer for reflection
• End scenes with hooks that create anticipation for what's next
• Layer subtext beneath dialogue - what's NOT said is often more powerful
• Use symbolic elements that reinforce themes and character arcs

🎭 EMOTIONAL RESONANCE TECHNIQUES:
• Connect current events to established character relationships
• Reference past victories and failures to add weight to current choices
• Use callbacks to previous memorable moments for emotional impact
• Create moments of vulnerability that reveal character depth
• Balance triumph with cost - every victory should have meaning
• Use silence and pauses effectively in dialogue and description
• Show character growth through how they handle similar situations differently

═══════════════════════════════════════════════════════════════════════════════`;
  }

  private analyzeEmotionalTrend(memories: any[]): {
    direction: string;
    intensity: number;
    nextBeat: string;
    investment: string;
  } {
    if (memories.length === 0) {
      return {
        direction: 'neutral',
        intensity: 5,
        nextBeat: 'establish emotional connection',
        investment: 'building'
      };
    }

    // Analyze emotional impact of recent memories
    const emotionalImpacts = memories
      .filter(m => m.emotionalImpact !== undefined)
      .map(m => m.emotionalImpact);

    if (emotionalImpacts.length === 0) {
      return {
        direction: 'steady',
        intensity: 5,
        nextBeat: 'introduce emotional stakes',
        investment: 'moderate'
      };
    }

    const avgImpact = emotionalImpacts.reduce((sum, impact) => sum + impact, 0) / emotionalImpacts.length;
    const trend = emotionalImpacts.length > 1 ?
      (emotionalImpacts[emotionalImpacts.length - 1] - emotionalImpacts[0]) : 0;

    let direction = 'steady';
    let nextBeat = 'maintain emotional engagement';

    if (trend > 2) {
      direction = 'rising dramatically';
      nextBeat = 'provide emotional release or climax';
    } else if (trend > 0) {
      direction = 'building tension';
      nextBeat = 'escalate stakes or provide character moment';
    } else if (trend < -2) {
      direction = 'declining rapidly';
      nextBeat = 'introduce new conflict or hope';
    } else if (trend < 0) {
      direction = 'cooling down';
      nextBeat = 'build toward next emotional peak';
    }

    return {
      direction,
      intensity: Math.max(1, Math.min(10, Math.abs(avgImpact) + 5)),
      nextBeat,
      investment: avgImpact > 5 ? 'high' : avgImpact > 0 ? 'moderate' : 'building'
    };
  }

  private determinePacingGuidance(memories: any[], gameState: any): {
    currentPace: string;
    recommendation: string;
    tensionLevel: number;
    actionNeeded: string;
  } {
    const recentActionTypes = memories.slice(-3).map(m => m.category || 'unknown');
    const combatCount = recentActionTypes.filter(type => type === 'combat').length;
    const dialogueCount = recentActionTypes.filter(type => type === 'dialogue' || type === 'character_interaction').length;
    const explorationCount = recentActionTypes.filter(type => type === 'discovery' || type === 'world_building').length;

    let currentPace = 'moderate';
    let recommendation = 'maintain current pacing';
    let tensionLevel = 5;
    let actionNeeded = 'continue current approach';

    if (combatCount >= 2) {
      currentPace = 'high-action';
      recommendation = 'slow down for character development or exploration';
      tensionLevel = 8;
      actionNeeded = 'provide breathing room and reflection';
    } else if (dialogueCount >= 2) {
      currentPace = 'character-focused';
      recommendation = 'introduce action or discovery element';
      tensionLevel = 3;
      actionNeeded = 'escalate stakes or add excitement';
    } else if (explorationCount >= 2) {
      currentPace = 'exploratory';
      recommendation = 'introduce character interaction or conflict';
      tensionLevel = 4;
      actionNeeded = 'add personal stakes or relationships';
    }

    return { currentPace, recommendation, tensionLevel, actionNeeded };
  }

  private generateForeshadowing(context: GameContext): string[] {
    const { gameState, characters, memories } = context;
    const foreshadowingElements: string[] = [];

    // Environmental foreshadowing
    foreshadowingElements.push('Subtle environmental details that hint at future challenges or revelations');

    // Character-based foreshadowing
    if (characters.length > 2) {
      foreshadowingElements.push('Character reactions or dialogue that hint at hidden knowledge or future conflicts');
    }

    // Plot-based foreshadowing
    const plotMemories = memories.filter(m => m.category === 'plot_event');
    if (plotMemories.length > 0) {
      foreshadowingElements.push('Callbacks to previous events that will gain new significance');
    }

    // Atmospheric foreshadowing
    foreshadowingElements.push('Weather, lighting, or mood changes that mirror upcoming story beats');

    // Symbolic foreshadowing
    foreshadowingElements.push('Objects, animals, or imagery that symbolically represent future themes');

    return foreshadowingElements;
  }

  // Specialized prompt templates for different scenario types
  private getSpecializedPromptTemplate(scenarioType: string): string {
    const templates = {
      combat: `
🗡️ COMBAT ENCOUNTER MASTERY 🗡️
• Describe combat as a deadly dance - every move has weight and consequence
• Use visceral, kinetic language that makes the player feel every clash of steel
• Show the environment as part of the battle - sparks flying, dust clouds, crumbling walls
• Each attack should have tactical implications and emotional stakes
• Describe not just what happens, but how it feels - the burn of muscles, the rush of adrenaline
• Make every enemy's actions logical and motivated, not random
• Build tension through near-misses and escalating danger
• Show the cost of battle - exhaustion, wounds, damaged equipment
• Victory should feel earned, defeat should feel meaningful
• Use short, punchy sentences for action, longer ones for dramatic moments`,

      exploration: `
🗺️ EXPLORATION & DISCOVERY EXCELLENCE 🗺️
• Paint the world with rich sensory details - what does this place smell like? Sound like?
• Every location should tell a story through environmental details
• Use the "rule of three" - give three interesting details about each new area
• Create a sense of history and mystery - who was here before? What happened?
• Make the player feel like an archaeologist uncovering secrets
• Use lighting and weather to create mood and atmosphere
• Hidden details reward careful observation and curiosity
• Each discovery should raise new questions while answering old ones
• Connect new locations to the broader world and story
• Make exploration feel rewarding through both treasure and knowledge`,

      social: `
🗣️ SOCIAL INTERACTION MASTERY 🗣️
• Every NPC should feel like a real person with their own agenda and personality
• Dialogue should reveal character through subtext and what's NOT said
• Use body language, facial expressions, and vocal tone to add depth
• Create social tension through conflicting goals and hidden motivations
• Show relationships evolving through repeated interactions
• Make conversations feel like verbal chess matches with stakes
• Use cultural details and social customs to add authenticity
• Let the player's reputation and past actions influence how NPCs react
• Create opportunities for both conflict and connection
• Make social victories feel as satisfying as combat victories`,

      puzzle: `
🧩 PUZZLE & MYSTERY CRAFTING 🧩
• Present clues organically through environment, dialogue, and observation
• Make puzzles feel integrated into the world, not artificially inserted
• Use multiple solution paths to reward different types of thinking
• Build complexity gradually - start simple, add layers
• Make failure informative - wrong attempts should teach something
• Connect puzzles to character knowledge and established world rules
• Use symbolism and metaphor to add depth to mechanical challenges
• Reward both logical deduction and creative thinking
• Make the "aha!" moment feel genuinely satisfying
• Connect puzzle solutions to character growth or story advancement`,

      dramatic: `
🎭 DRAMATIC MOMENT ORCHESTRATION 🎭
• Build emotional tension through pacing - slow buildup, explosive release
• Use silence and pauses as powerfully as words and action
• Connect current events to established character relationships and history
• Make choices feel weighty - show the emotional cost of decisions
• Use environmental details to mirror internal emotional states
• Create moments of vulnerability that reveal character depth
• Build to emotional crescendos through layered revelations
• Use callbacks to previous events for maximum emotional impact
• Show character growth through how they handle similar situations differently
• Make every dramatic beat serve both character and plot development`,

      mystery: `
🔍 MYSTERY & INTRIGUE WEAVING 🔍
• Plant clues that seem innocent until viewed in context
• Use red herrings that feel meaningful, not arbitrary
• Build suspense through what you don't reveal, not just what you do
• Make every character a potential suspect with believable motives
• Use foreshadowing to make revelations feel inevitable in hindsight
• Create multiple layers of mystery - solve one, reveal another
• Use environmental storytelling to provide crucial evidence
• Make the investigation process as engaging as the solution
• Connect mysteries to character backstories and motivations
• Ensure revelations recontextualize earlier events in meaningful ways`
    };

    return templates[scenarioType] || '';
  }

  // Enhanced response generation with scenario-specific guidance
  async generateScenarioResponse(
    context: GameContext,
    scenarioType: 'combat' | 'exploration' | 'social' | 'puzzle' | 'dramatic' | 'mystery'
  ): Promise<AIResponse> {
    const messages = this.buildContextMessages(context);

    // Add scenario-specific guidance to the system prompt
    if (messages[0]) {
      const specializedGuidance = this.getSpecializedPromptTemplate(scenarioType);
      messages[0].content += `\n\n${specializedGuidance}`;
    }

    const responseText = await this.makeAPICall(messages);
    return this.parseAIResponse(responseText);
  }

  // Enhanced memory context building with deeper analysis and continuity
  private buildEnhancedMemoryContext(
    memories: MemoryEntry[],
    gameState: GameState,
    characters: Character[],
    characterConsistency: Record<string, CharacterConsistency>
  ): string {
    if (memories.length === 0) {
      return 'This is the beginning of your legendary adventure. Every choice you make will become part of your story.';
    }

    // Categorize memories with enhanced analysis
    const categorizedMemories = {
      characterDefining: memories.filter(m => m.isCharacterDefining).slice(-5),
      highImpact: memories.filter(m => m.importance >= 8).slice(-3),
      emotional: memories.filter(m => m.emotionalImpact && Math.abs(m.emotionalImpact) >= 5).slice(-3),
      location: memories.filter(m => m.location === gameState.currentLocation).slice(-3),
      relationships: memories.filter(m => m.category === 'relationship_change' || m.category === 'character_interaction').slice(-4),
      plot: memories.filter(m => m.category === 'plot_event' || m.category === 'quest_progress').slice(-3),
      recent: memories.slice(-5)
    };

    // Build character relationship context
    const relationshipContext = this.buildRelationshipContext(characters, memories);

    // Build emotional journey context
    const emotionalJourney = this.buildEmotionalJourney(categorizedMemories.emotional);

    // Build character growth context
    const characterGrowth = this.buildCharacterGrowthContext(categorizedMemories.characterDefining);

    const contextSections = [
      categorizedMemories.characterDefining.length ?
        `🌟 CHARACTER-DEFINING MOMENTS:\n${categorizedMemories.characterDefining.map(m =>
          `• ${m.content} (Impact: ${m.importance}/10)`
        ).join('\n')}` : '',

      categorizedMemories.highImpact.length ?
        `⚡ PIVOTAL EVENTS:\n${categorizedMemories.highImpact.map(m =>
          `• ${m.content} (Significance: ${m.importance}/10)`
        ).join('\n')}` : '',

      emotionalJourney ? `💫 EMOTIONAL JOURNEY:\n${emotionalJourney}` : '',

      relationshipContext ? `🤝 RELATIONSHIP DYNAMICS:\n${relationshipContext}` : '',

      characterGrowth ? `📈 CHARACTER GROWTH:\n${characterGrowth}` : '',

      categorizedMemories.location.length ?
        `🗺️ LOCATION HISTORY (${gameState.currentLocation}):\n${categorizedMemories.location.map(m =>
          `• ${m.content}`
        ).join('\n')}` : '',

      categorizedMemories.plot.length ?
        `📖 STORY PROGRESSION:\n${categorizedMemories.plot.map(m =>
          `• ${m.content}`
        ).join('\n')}` : '',

      `🕐 RECENT EVENTS:\n${categorizedMemories.recent.map(m =>
        `• ${m.content}${m.emotionalImpact ? ` (Emotional impact: ${m.emotionalImpact > 0 ? '+' : ''}${m.emotionalImpact})` : ''}`
      ).join('\n')}`
    ].filter(Boolean);

    return contextSections.join('\n\n');
  }

  private buildRelationshipContext(characters: Character[], memories: MemoryEntry[]): string {
    const relationshipMemories = memories.filter(m =>
      m.category === 'character_interaction' ||
      m.category === 'relationship_change' ||
      m.relatedCharacters.length > 0
    ).slice(-5);

    if (relationshipMemories.length === 0) return '';

    const relationshipMap = new Map<string, string[]>();

    relationshipMemories.forEach(memory => {
      memory.relatedCharacters.forEach(charId => {
        const character = characters.find(c => c.id === charId);
        if (character && character.type !== 'player') {
          if (!relationshipMap.has(character.name)) {
            relationshipMap.set(character.name, []);
          }
          relationshipMap.get(character.name)!.push(memory.content);
        }
      });
    });

    const relationshipEntries = Array.from(relationshipMap.entries()).map(([name, events]) =>
      `• ${name}: ${events[events.length - 1]}`
    );

    return relationshipEntries.join('\n');
  }

  private buildEmotionalJourney(emotionalMemories: MemoryEntry[]): string {
    if (emotionalMemories.length === 0) return '';

    const journey = emotionalMemories.map(memory => {
      const impact = memory.emotionalImpact || 0;
      const emotion = impact > 5 ? '🔥 High' : impact > 0 ? '✨ Positive' : impact < -5 ? '💔 Intense' : '🌊 Negative';
      return `${emotion}: ${memory.content}`;
    });

    return journey.join('\n• ');
  }

  private buildCharacterGrowthContext(definingMemories: MemoryEntry[]): string {
    if (definingMemories.length === 0) return '';

    const growthMoments = definingMemories.map(memory =>
      `• ${memory.content} - This shaped who you are becoming`
    );

    return growthMoments.join('\n');
  }

  // Generate multiple character responses in sequence for complex scenes
  async generateMultiCharacterResponse(
    context: GameContext,
    activeCharacters: Array<{ id: string; name: string; type: 'npc' | 'companion' | 'enemy' | 'merchant' }>
  ): Promise<AIResponse[]> {
    const responses: AIResponse[] = [];
    
    // Generate Game Master response first
    const gmResponse = await this.generateResponse(context);
    responses.push(gmResponse);
    
    // Generate responses for each active character
    for (const character of activeCharacters) {
      try {
        const characterResponse = await this.generateCharacterResponse(
          context,
          character.id,
          character.name,
          character.type
        );
        responses.push(characterResponse);
        
        // Small delay between API calls to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 500));
      } catch (error) {
        console.warn(`Failed to generate response for character ${character.name}:`, error);
      }
    }
    
    return responses;
  }

  // Enhanced response consistency validation with character tracking
  validateResponseConsistency(response: AIResponse, context: GameContext): {
    isValid: boolean;
    issues: string[];
    characterConsistencyIssues: string[];
  } {
    const issues: string[] = [];
    const characterConsistencyIssues: string[] = [];
    
    try {
      // Validate character consistency
      const character = context.characters.find(c => c.id === response.characterId);
      const consistency = context.characterConsistency[response.characterId];
      
      if (character && consistency) {
        // Check for contradictions in established traits
        if (response.gameStateUpdates?.characterUpdates) {
          const updates = response.gameStateUpdates.characterUpdates[0]; // Get first character update
          
          // Check physical description consistency
          if (updates.physicalDescription && character.physicalDescription && 
              updates.physicalDescription !== character.physicalDescription) {
            characterConsistencyIssues.push(`Physical description contradiction for ${character.name}`);
          }
          
          // Check personality consistency
          if (updates.personality && character.personality && 
              !this.isPersonalityConsistent(character.personality, updates.personality)) {
            characterConsistencyIssues.push(`Personality contradiction for ${character.name}`);
          }
          
          // Check backstory consistency
          if (updates.backstory && character.backstory && 
              !this.isBackstoryConsistent(character.backstory, updates.backstory)) {
            characterConsistencyIssues.push(`Backstory contradiction for ${character.name}`);
          }
        }
      }

      // Validate game state updates
      if (response.gameStateUpdates) {
        const { playerStats, inventory, gameState } = response.gameStateUpdates;
        
        // Check for impossible stat values
        if (playerStats) {
          if (playerStats.health !== undefined && playerStats.health < 0) {
            issues.push('Player health cannot be negative');
          }
          if (playerStats.mana !== undefined && playerStats.mana < 0) {
            issues.push('Player mana cannot be negative');
          }
          if (playerStats.level !== undefined && playerStats.level < 1) {
            issues.push('Player level cannot be less than 1');
          }
        }
        
        // Validate inventory items
        if (inventory) {
          for (const item of inventory) {
            if (!item.id || !item.name) {
              issues.push('Invalid inventory item: missing id or name');
            }
            if (item.quantity !== undefined && item.quantity < 1) {
              issues.push(`Invalid item quantity for ${item.name}`);
            }
          }
        }
      }
      
      return {
        isValid: issues.length === 0 && characterConsistencyIssues.length === 0,
        issues,
        characterConsistencyIssues
      };
    } catch (error) {
      console.error('Error validating response consistency:', error);
      return {
        isValid: false,
        issues: ['Validation error occurred'],
        characterConsistencyIssues: []
      };
    }
  }
  
  private isPersonalityConsistent(existing: string, new_personality: string): boolean {
    // Simple consistency check - can be enhanced with NLP
    const existingTraits = existing.toLowerCase().split(/[,;.]/).map(t => t.trim());
    const newTraits = new_personality.toLowerCase().split(/[,;.]/).map(t => t.trim());
    
    // Check for direct contradictions (basic implementation)
    const contradictions = [
      ['friendly', 'hostile'], ['kind', 'cruel'], ['brave', 'cowardly'],
      ['honest', 'deceptive'], ['calm', 'aggressive'], ['wise', 'foolish']
    ];
    
    for (const [trait1, trait2] of contradictions) {
      if (existingTraits.some(t => t.includes(trait1)) && newTraits.some(t => t.includes(trait2))) {
        return false;
      }
      if (existingTraits.some(t => t.includes(trait2)) && newTraits.some(t => t.includes(trait1))) {
        return false;
      }
    }
    
    return true;
  }
  
  private isBackstoryConsistent(existing: string, new_backstory: string): boolean {
    // Simple consistency check - ensure new backstory doesn't contradict key facts
    // This is a basic implementation that can be enhanced
    return !new_backstory.toLowerCase().includes('never') || 
           !existing.toLowerCase().includes('always');
  }
}

export const aiService = new AIService();
export default aiService;
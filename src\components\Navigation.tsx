import { MessageSquare, Package, User } from 'lucide-react';
import { useGameStore } from '@/lib/store';
import { cn } from '@/lib/utils';

const Navigation = () => {
  const { currentPage, setCurrentPage } = useGameStore();

  const navItems = [
    {
      id: 'chat' as const,
      label: 'Adventure',
      icon: MessageSquare,
      description: 'Chat with the Game Master',
    },
    {
      id: 'inventory' as const,
      label: 'Inventory',
      icon: Package,
      description: 'Manage your items and equipment',
    },
    {
      id: 'stats' as const,
      label: 'Character',
      icon: User,
      description: 'View your stats and achievements',
    },
  ];

  return (
    <nav className="bg-slate-900 border-b border-purple-500/20 px-4 py-3">
      <div className="max-w-6xl mx-auto">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-br from-purple-600 to-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">AI</span>
            </div>
            <h1 className="text-xl font-bold text-white">Dungeon Master</h1>
          </div>
          
          <div className="flex items-center space-x-1">
            {navItems.map((item) => {
              const Icon = item.icon;
              const isActive = currentPage === item.id;
              
              return (
                <button
                  key={item.id}
                  onClick={() => setCurrentPage(item.id)}
                  className={cn(
                    'flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-200',
                    'hover:bg-purple-600/20 focus:outline-none focus:ring-2 focus:ring-purple-500',
                    isActive
                      ? 'bg-purple-600 text-white shadow-lg'
                      : 'text-gray-300 hover:text-white'
                  )}
                  title={item.description}
                >
                  <Icon className="w-4 h-4" />
                  <span className="font-medium">{item.label}</span>
                </button>
              );
            })}
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navigation;
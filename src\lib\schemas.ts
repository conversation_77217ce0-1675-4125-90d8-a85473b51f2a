import { z } from 'zod';

// Player Stats Schema
export const PlayerStatsSchema = z.object({
  health: z.number().min(0),
  maxHealth: z.number().min(1),
  mana: z.number().min(0),
  maxMana: z.number().min(1),
  level: z.number().min(1),
  experience: z.number().min(0),
  experienceToNext: z.number().min(1),
  strength: z.number().min(1),
  dexterity: z.number().min(1),
  intelligence: z.number().min(1),
  constitution: z.number().min(1),
});

// Item Schema
export const ItemSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  type: z.enum(['weapon', 'armor', 'accessory', 'consumable', 'quest', 'misc']),
  rarity: z.enum(['common', 'uncommon', 'rare', 'epic', 'legendary']),
  quantity: z.number().min(1).default(1),
  stats: z.object({
    attack: z.number().optional(),
    defense: z.number().optional(),
    health: z.number().optional(),
    mana: z.number().optional(),
    strength: z.number().optional(),
    dexterity: z.number().optional(),
    intelligence: z.number().optional(),
    constitution: z.number().optional(),
  }).optional(),
  equipped: z.boolean().default(false),
  equipSlot: z.enum(['weapon', 'armor', 'accessory']).optional(),
});

// Equipment Slots Schema
export const EquipmentSchema = z.object({
  weapon: ItemSchema.optional(),
  armor: ItemSchema.optional(),
  accessory: ItemSchema.optional(),
});

// Achievement Schema
export const AchievementSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  completed: z.boolean().default(false),
  completedAt: z.date().optional(),
  category: z.enum(['combat', 'exploration', 'story', 'collection', 'social']),
});

// Enhanced Character Schema with detailed tracking
export const CharacterSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: z.enum(['player', 'gamemaster', 'npc', 'companion', 'enemy', 'merchant']),
  personality: z.string().optional(),
  relationship: z.number().min(-100).max(100).optional(), // -100 to 100
  lastSeen: z.date().optional(),
  // Enhanced character tracking
  physicalDescription: z.string().optional(),
  backstory: z.string().optional(),
  motivations: z.array(z.string()).optional(),
  fears: z.array(z.string()).optional(),
  skills: z.array(z.string()).optional(),
  occupation: z.string().optional(),
  location: z.string().optional(),
  status: z.enum(['alive', 'dead', 'missing', 'unknown']).default('alive'),
  relationships: z.record(z.string(), z.object({
    characterId: z.string(),
    relationshipType: z.enum(['friend', 'enemy', 'neutral', 'family', 'romantic', 'ally', 'rival']),
    strength: z.number().min(-100).max(100),
    notes: z.string().optional(),
  })).optional(),
  characterTraits: z.array(z.object({
    trait: z.string(),
    intensity: z.number().min(1).max(10),
    description: z.string().optional(),
  })).optional(),
  speechPatterns: z.object({
    vocabulary: z.enum(['simple', 'moderate', 'complex', 'archaic', 'technical']).optional(),
    tone: z.enum(['formal', 'casual', 'aggressive', 'gentle', 'mysterious', 'cheerful']).optional(),
    quirks: z.array(z.string()).optional(),
  }).optional(),
  memoryImportance: z.number().min(1).max(10).default(5),
  firstMet: z.date().optional(),
  interactionCount: z.number().min(0).default(0),
  lastInteractionSummary: z.string().optional(),
});

// Message Schema
export const MessageSchema = z.object({
  id: z.string(),
  content: z.string(),
  timestamp: z.date(),
  characterId: z.string(),
  characterName: z.string(),
  characterType: z.enum(['player', 'gamemaster', 'npc', 'companion', 'enemy', 'merchant']),
  isSystemMessage: z.boolean().default(false),
});

// Game State Schema
export const GameStateSchema = z.object({
  currentLocation: z.string(),
  currentScene: z.string(),
  activeEffects: z.array(z.string()),
  questLog: z.array(z.object({
    id: z.string(),
    name: z.string(),
    description: z.string(),
    status: z.enum(['active', 'completed', 'failed']),
    progress: z.number().min(0).max(100),
  })),
  worldState: z.record(z.string(), z.any()),
});

// Enhanced Memory Entry Schema with sophisticated categorization
export const MemoryEntrySchema = z.object({
  id: z.string(),
  content: z.string(),
  timestamp: z.date(),
  importance: z.number().min(1).max(10),
  tags: z.array(z.string()),
  relatedCharacters: z.array(z.string()),
  location: z.string().optional(),
  // Enhanced memory categorization
  category: z.enum([
    'character_introduction',
    'character_interaction',
    'character_development',
    'plot_event',
    'world_building',
    'combat',
    'dialogue',
    'discovery',
    'relationship_change',
    'quest_progress',
    'emotional_moment',
    'conflict',
    'resolution'
  ]),
  emotionalImpact: z.number().min(-10).max(10).optional(),
  consequences: z.array(z.string()).optional(),
  relatedMemories: z.array(z.string()).optional(),
  embedding: z.array(z.number()).optional(), // For semantic search
  keywords: z.array(z.string()).optional(),
  summary: z.string().optional(),
  isCharacterDefining: z.boolean().default(false),
  accessCount: z.number().min(0).default(0),
  lastAccessed: z.date().optional(),
});

// Character Consistency Tracker Schema
export const CharacterConsistencySchema = z.object({
  characterId: z.string(),
  establishedTraits: z.record(z.string(), z.object({
    value: z.string(),
    confidence: z.number().min(0).max(1),
    firstEstablished: z.date(),
    lastConfirmed: z.date(),
    contradictions: z.array(z.object({
      contradictoryValue: z.string(),
      timestamp: z.date(),
      resolved: z.boolean().default(false),
    })),
  })),
  consistencyScore: z.number().min(0).max(1),
  lastValidated: z.date(),
});

// Long-term Memory System Schema
export const LongTermMemorySchema = z.object({
  characterMemories: z.record(z.string(), z.array(z.string())), // characterId -> memoryIds
  locationMemories: z.record(z.string(), z.array(z.string())), // location -> memoryIds
  plotMemories: z.array(z.string()),
  relationshipMemories: z.record(z.string(), z.array(z.string())), // relationship -> memoryIds
  semanticIndex: z.record(z.string(), z.array(z.string())), // keyword -> memoryIds
  memoryGraph: z.record(z.string(), z.array(z.string())), // memoryId -> related memoryIds
  importantMoments: z.array(z.object({
    memoryId: z.string(),
    significance: z.number().min(1).max(10),
    impact: z.string(),
  })),
});

// AI Response Schema
export const AIResponseSchema = z.object({
  message: z.string().optional(),
  characterId: z.string().optional(),
  characterName: z.string().optional(),
  characterType: z.enum(['gamemaster', 'npc', 'companion', 'enemy', 'merchant']).optional(),
  gameStateUpdates: z.object({
    playerStats: PlayerStatsSchema.partial().optional(),
    inventory: z.array(ItemSchema).optional(),
    equipment: EquipmentSchema.partial().optional(),
    gameState: GameStateSchema.partial().optional(),
    newAchievements: z.array(AchievementSchema).optional(),
    newMemories: z.array(MemoryEntrySchema.omit({ id: true, timestamp: true, accessCount: true, lastAccessed: true })).optional(),
    memoryUpdates: z.array(MemoryEntrySchema.omit({ id: true, timestamp: true, accessCount: true, lastAccessed: true })).optional(),
    characterUpdates: z.array(CharacterSchema.partial()).optional(),
  }).optional(),
  availableActions: z.array(z.string()).optional(),
});

// API Request Schema with increased token limits
export const APIRequestSchema = z.object({
  model: z.literal('glm-4.5-flash'),
  messages: z.array(z.object({
    role: z.enum(['user', 'assistant', 'system']),
    content: z.string(),
  })),
  temperature: z.number().min(0).max(2).optional(),
  max_tokens: z.number().min(1).max(65536).optional(), // Increased to 64k
});

// Export types
export type PlayerStats = z.infer<typeof PlayerStatsSchema>;
export type Item = z.infer<typeof ItemSchema>;
export type Equipment = z.infer<typeof EquipmentSchema>;
export type Achievement = z.infer<typeof AchievementSchema>;
export type Character = z.infer<typeof CharacterSchema>;
export type Message = z.infer<typeof MessageSchema>;
export type GameState = z.infer<typeof GameStateSchema>;
export type MemoryEntry = z.infer<typeof MemoryEntrySchema>;
export type AIResponse = z.infer<typeof AIResponseSchema>;
export type APIRequest = z.infer<typeof APIRequestSchema>;
export type CharacterConsistency = z.infer<typeof CharacterConsistencySchema>;
export type LongTermMemory = z.infer<typeof LongTermMemorySchema>;
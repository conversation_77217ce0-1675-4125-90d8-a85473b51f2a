import { useState, useRef, useEffect } from 'react';
import { Send, MapPin, Heart, Zap, Loader2, Sword, Users, ShoppingBag, TreePine, Eye, Save, Upload, RotateCcw, Menu, X, Trash2 } from 'lucide-react';
import { useGameStore } from '@/lib/store';
import { aiService } from '@/lib/aiService';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

const ChatInterface = () => {
  const {
    messages,
    playerStats,
    gameState,
    characters,
    memories,
    characterConsistency,
    longTermMemory,
    isLoading,
    addMessage,
    updatePlayerStats,
    addItem,
    updateGameState,
    addMemory,
    addAchievement,
    addCharacter,
    setLoading,
    saveGame,
    loadGame,
    resetGame,
    initializeGame,
    getSavedGames,
    deleteSavedGame,
    validateCharacterConsistency,
    getContextualMemories,
    buildCharacterContext,
  } = useGameStore();

  const [inputValue, setInputValue] = useState('');
  const [showGameMenu, setShowGameMenu] = useState(false);
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [showLoadDialog, setShowLoadDialog] = useState(false);
  const [saveSlotName, setSaveSlotName] = useState('');
  const [savedGames, setSavedGames] = useState<string[]>([]);
  const [pendingAchievements, setPendingAchievements] = useState<any[]>([]);
  const [pendingError, setPendingError] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Handle toast notifications outside of render cycle
  useEffect(() => {
    if (pendingAchievements.length > 0) {
      pendingAchievements.forEach(achievement => {
        toast.success(`Achievement Unlocked: ${achievement.name}!`);
      });
      setPendingAchievements([]);
    }
  }, [pendingAchievements]);

  useEffect(() => {
    if (pendingError) {
      toast.error(pendingError);
      setPendingError(null);
    }
  }, [pendingError]);

  const getCharacterStyle = (characterType: string) => {
    switch (characterType) {
      case 'player':
        return 'bg-blue-600 text-white ml-auto';
      case 'gamemaster':
        return 'bg-purple-600 text-white';
      case 'npc':
        return 'bg-green-600 text-white';
      case 'companion':
        return 'bg-orange-600 text-white';
      case 'enemy':
        return 'bg-red-600 text-white';
      case 'merchant':
        return 'bg-yellow-600 text-black';
      default:
        return 'bg-gray-600 text-white';
    }
  };

  const handleSendMessage = async (messageOverride?: string) => {
    const userMessage = messageOverride || inputValue.trim();
    if (!userMessage || isLoading) return;

    if (!messageOverride) {
      setInputValue('');
    }

    // Prepare the new user message object
    const newUserMessage = {
      content: userMessage,
      characterId: 'player',
      characterName: 'You',
      characterType: 'player' as const,
    };

    // Include the new message in the context before the state update takes effect
    const updatedMessages = [...messages, newUserMessage];

    // Add user message to state for immediate UI feedback
    addMessage(newUserMessage);

    setLoading(true);

    try {
      // Build enhanced context for AI with memory and consistency data
      const context = {
        playerStats,
        gameState,
        recentMessages: updatedMessages,
        characters,
        memories,
        characterConsistency,
        longTermMemory,
        playerInput: userMessage,
      };

      // Generate AI response
      const aiResponse = await aiService.generateResponse(context);
      
      // Enhanced response consistency validation
      const validation = aiService.validateResponseConsistency(aiResponse, context);
      if (!validation.isValid) {
        console.warn('Response consistency issues:', validation.issues, validation.characterConsistencyIssues);
        
        // Log issues but don't fail completely - allow game to continue with warnings
        if (validation.characterConsistencyIssues.length > 0) {
          toast.warning(`Character consistency warning: ${validation.characterConsistencyIssues[0]}`);
        }
        if (validation.issues.length > 0) {
          console.warn('Game state validation issues:', validation.issues);
        }
      }

      // Add AI message with full response data for proper rendering
      addMessage({
        content: JSON.stringify(aiResponse),
        characterId: aiResponse.characterId,
        characterName: aiResponse.characterName,
        characterType: aiResponse.characterType,
      });

      // Apply game state updates
      if (aiResponse.gameStateUpdates) {
        const updates = aiResponse.gameStateUpdates;
        
        if (updates.playerStats) {
          updatePlayerStats(updates.playerStats);
        }
        
        if (updates.inventory) {
          updates.inventory.forEach(item => addItem(item));
        }
        
        if (updates.gameState) {
          updateGameState(updates.gameState);
        }
        
        if (updates.newAchievements) {
          updates.newAchievements.forEach(achievement => {
            addAchievement(achievement);
          });
          // Queue achievements for toast notifications
          setPendingAchievements(updates.newAchievements);
        }
        
        if (updates.newMemories) {
          updates.newMemories.forEach(memory => addMemory(memory));
        }
        
        // Handle memory updates from AI response (now nested in gameStateUpdates)
        if (updates.memoryUpdates) {
          updates.memoryUpdates.forEach(memory => {
            const enhancedMemory = {
              ...memory,
              relatedCharacters: memory.relatedCharacters || [aiResponse.characterId],
              location: memory.location || gameState.currentLocation,
              category: (memory.category as any) || 'world_building',
              emotionalImpact: memory.emotionalImpact || 0,
              isCharacterDefining: memory.isCharacterDefining || false,
            };
            addMemory(enhancedMemory as any);
          });
        }
        
        // Handle character updates from AI response (now nested in gameStateUpdates)
        if (updates.characterUpdates) {
          updates.characterUpdates.forEach(characterUpdate => {
            const existingCharacter = characters.find(c => c.id === characterUpdate.id);
            if (existingCharacter) {
              // Validate character consistency before updating
              const consistencyCheck = validateCharacterConsistency(existingCharacter, characterUpdate);
              if (consistencyCheck.isConsistent) {
                // Merge new character information
                addCharacter({
                  ...existingCharacter,
                  ...characterUpdate,
                  lastSeen: new Date(),
                });
              } else {
                console.warn('Character update rejected due to inconsistency:', consistencyCheck.contradictions);
                toast.warning(`Character update blocked: ${consistencyCheck.contradictions[0] || 'Inconsistent with established traits'}`);
              }
            } else if (characterUpdate.id) {
              // Add new character if it doesn't exist
              addCharacter({
                ...characterUpdate,
                lastSeen: new Date(),
              } as any);
            }
          });
        }
      }

      // Add any new characters mentioned in the response with enhanced tracking
      if (aiResponse.characterId !== 'gamemaster' && aiResponse.characterId !== 'player' && !characters.find(c => c.id === aiResponse.characterId)) {
        const newCharacter = {
          id: aiResponse.characterId,
          name: aiResponse.characterName,
          type: aiResponse.characterType,
          lastSeen: new Date(),
        };
        
        addCharacter(newCharacter);
        
        // Create a memory for character introduction
        addMemory({
          content: `Met ${aiResponse.characterName}, a ${aiResponse.characterType} in ${gameState.currentLocation}`,
          relatedCharacters: [aiResponse.characterId],
          location: gameState.currentLocation,
          category: 'character_introduction',
          emotionalImpact: 0,
          isCharacterDefining: true,
          tags: ['character', 'introduction', aiResponse.characterType],
        });
      }

    } catch (error) {
      console.error('Error generating AI response:', error);
      setPendingError('Failed to get response from Game Master. Please try again.');
      
      // Add error message
      addMessage({
        content: 'The Game Master seems to be having trouble responding. Please try rephrasing your action or try again.',
        characterId: 'gamemaster',
        characterName: 'Game Master',
        characterType: 'gamemaster',
        isSystemMessage: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const adjustTextareaHeight = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`;
    }
  };

  useEffect(() => {
    adjustTextareaHeight();
  }, [inputValue]);

  useEffect(() => {
    if (showLoadDialog) {
      setSavedGames(getSavedGames());
    }
  }, [showLoadDialog, getSavedGames]);

  const [pendingSuccessMessage, setPendingSuccessMessage] = useState<string | null>(null);

  // Handle success toast notifications
  useEffect(() => {
    if (pendingSuccessMessage) {
      toast.success(pendingSuccessMessage);
      setPendingSuccessMessage(null);
    }
  }, [pendingSuccessMessage]);

  const handleQuickSave = () => {
    saveGame('quicksave');
    setPendingSuccessMessage('Game saved!');
    setShowGameMenu(false);
  };

  const handleQuickLoad = () => {
    if (loadGame('quicksave')) {
      setPendingSuccessMessage('Game loaded!');
    } else {
      setPendingError('No quicksave found!');
    }
    setShowGameMenu(false);
  };

  const handleSaveToSlot = () => {
    if (!saveSlotName.trim()) {
      setPendingError('Please enter a save name!');
      return;
    }
    saveGame(saveSlotName.trim());
    setPendingSuccessMessage(`Game saved as "${saveSlotName.trim()}"!`);
    setSaveSlotName('');
    setShowSaveDialog(false);
    setShowGameMenu(false);
  };

  const handleLoadFromSlot = (slotName: string) => {
    if (loadGame(slotName)) {
      setPendingSuccessMessage(`Game "${slotName}" loaded!`);
    } else {
      setPendingError('Failed to load game!');
    }
    setShowLoadDialog(false);
    setShowGameMenu(false);
  };

  const handleDeleteSave = (slotName: string) => {
    deleteSavedGame(slotName);
    setSavedGames(getSavedGames());
    setPendingSuccessMessage(`Save "${slotName}" deleted!`);
  };

  const handleNewGame = () => {
    if (confirm('Are you sure you want to start a new game? All unsaved progress will be lost.')) {
      setLoading(true);
      resetGame();
      
      // Ensure proper state synchronization before initializing
      setTimeout(() => {
        initializeGame();
        setPendingSuccessMessage('New game started!');
        setLoading(false);
      }, 200); // Increased delay to ensure state is fully reset
      
      setShowGameMenu(false);
    }
  };

  const getActionIcon = (action: string) => {
    const actionLower = action.toLowerCase();
    if (actionLower.includes('attack') || actionLower.includes('fight') || actionLower.includes('combat')) {
      return <Sword className="w-3 h-3" />;
    }
    if (actionLower.includes('talk') || actionLower.includes('speak') || actionLower.includes('villager')) {
      return <Users className="w-3 h-3" />;
    }
    if (actionLower.includes('inventory') || actionLower.includes('item') || actionLower.includes('shop')) {
      return <ShoppingBag className="w-3 h-3" />;
    }
    if (actionLower.includes('explore') || actionLower.includes('forest') || actionLower.includes('path')) {
      return <TreePine className="w-3 h-3" />;
    }
    if (actionLower.includes('look') || actionLower.includes('examine') || actionLower.includes('search')) {
      return <Eye className="w-3 h-3" />;
    }
    return <Eye className="w-3 h-3" />;
  };

  const handleActionClick = (action: string) => {
    if (!isLoading) {
      setInputValue(action);
      // Auto-send the action
      setTimeout(() => {
        if (!isLoading) {
          handleSendMessage(action);
        }
      }, 100);
    }
  };

  const tryParseJSON = (content: string) => {
    try {
      // Try to find JSON in the content
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
    } catch (error) {
      // Not valid JSON, return null
    }
    return null;
  };

  const renderMessageContent = (message: any) => {
    // Try to parse JSON from the message content
    const jsonData = tryParseJSON(message.content);
    
    if (jsonData && jsonData.message && message.characterType === 'gamemaster') {
      return (
        <div className="space-y-3">
          {/* Main narrative message */}
          <div className="text-white leading-relaxed">
            {jsonData.message}
          </div>
          
          {/* Available Actions */}
          {jsonData.availableActions && jsonData.availableActions.length > 0 && (
            <div className="mt-4">
              <div className="text-xs font-semibold text-purple-200 mb-2 uppercase tracking-wide">
                Available Actions
              </div>
              <div className="flex flex-wrap gap-2">
                {jsonData.availableActions.map((action: string, index: number) => (
                  <button
                    key={index}
                    onClick={() => handleActionClick(action)}
                    disabled={isLoading}
                    className="inline-flex items-center space-x-1 px-3 py-1.5 bg-purple-500/20 hover:bg-purple-500/30 border border-purple-400/30 rounded-full text-xs text-purple-200 hover:text-white transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {getActionIcon(action)}
                    <span>{action}</span>
                  </button>
                ))}
              </div>
            </div>
          )}
          
          {/* Game State Updates */}
          {jsonData.gameStateUpdates && (
            <div className="mt-4 p-3 bg-purple-900/20 rounded-lg border border-purple-500/20">
              <div className="text-xs font-semibold text-purple-200 mb-2 uppercase tracking-wide">
                Game Updates
              </div>
              <div className="text-xs text-purple-300 space-y-1">
                {jsonData.gameStateUpdates.playerStats && (
                  <div>Player stats updated</div>
                )}
                {jsonData.gameStateUpdates.inventory && (
                  <div>New items added to inventory</div>
                )}
                {jsonData.gameStateUpdates.gameState && (
                  <div>Location or scene changed</div>
                )}
                {jsonData.gameStateUpdates.newAchievements && (
                  <div>New achievements unlocked!</div>
                )}
              </div>
            </div>
          )}
        </div>
      );
    }
    
    // Fallback to regular message display
    return message.content;
  };

  return (
    <div className="flex h-screen bg-slate-900">
      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Messages Container */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.length === 0 && (
            <div className="text-center text-gray-400 mt-8">
              <p className="text-lg mb-2">Welcome to your AI Dungeon Adventure!</p>
              <p>Type your first action to begin your journey...</p>
            </div>
          )}
          
          {messages.map((message) => (
            <div
              key={message.id}
              className={cn(
                'flex',
                message.characterType === 'player' ? 'justify-end' : 'justify-start'
              )}
            >
              <div
                className={cn(
                  'max-w-xs lg:max-w-md xl:max-w-2xl px-4 py-3 rounded-lg shadow-lg',
                  getCharacterStyle(message.characterType),
                  message.isSystemMessage && 'opacity-75 italic',
                  message.characterType === 'gamemaster' && 'max-w-2xl'
                )}
              >
                {message.characterType !== 'player' && (
                  <div className="text-xs font-semibold mb-1 opacity-90">
                    {message.characterName}
                  </div>
                )}
                <div className="whitespace-pre-wrap break-words">
                  {renderMessageContent(message)}
                </div>
                <div className="text-xs opacity-70 mt-1">
                  {new Date(message.timestamp).toLocaleTimeString()}
                </div>
              </div>
            </div>
          ))}
          
          {isLoading && (
            <div className="flex justify-start">
              <div className="bg-purple-600 text-white px-4 py-3 rounded-lg shadow-lg max-w-xs">
                <div className="flex items-center space-x-2">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>Game Master is thinking...</span>
                </div>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>

        {/* Input Area */}
        <div className="border-t border-purple-500/20 p-4 bg-slate-800">
          <div className="flex items-end space-x-3">
            <div className="flex-1">
              <textarea
                ref={textareaRef}
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Describe your action..."
                className="w-full px-4 py-3 bg-slate-700 border border-purple-500/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
                rows={1}
                disabled={isLoading}
              />
            </div>
            <button
              onClick={() => handleSendMessage()}
              disabled={!inputValue.trim() || isLoading}
              className="px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <Send className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Game Context Sidebar */}
      <div className="w-80 bg-slate-800 border-l border-purple-500/20 p-4 overflow-y-auto">
        <div className="space-y-6">
          {/* Game Menu */}
          <div className="bg-slate-700 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold text-white">Game Menu</h3>
              <button
                onClick={() => setShowGameMenu(!showGameMenu)}
                className="p-1 text-gray-400 hover:text-white transition-colors"
              >
                {showGameMenu ? <X className="w-4 h-4" /> : <Menu className="w-4 h-4" />}
              </button>
            </div>
            
            {showGameMenu && (
              <div className="space-y-2">
                <button
                  onClick={handleQuickSave}
                  className="w-full flex items-center space-x-2 px-3 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
                >
                  <Save className="w-4 h-4" />
                  <span>Quick Save</span>
                </button>
                
                <button
                  onClick={handleQuickLoad}
                  className="w-full flex items-center space-x-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                >
                  <Upload className="w-4 h-4" />
                  <span>Quick Load</span>
                </button>
                
                <button
                  onClick={() => setShowSaveDialog(true)}
                  className="w-full flex items-center space-x-2 px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                >
                  <Save className="w-4 h-4" />
                  <span>Save Game</span>
                </button>
                
                <button
                  onClick={() => setShowLoadDialog(true)}
                  className="w-full flex items-center space-x-2 px-3 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors"
                >
                  <Upload className="w-4 h-4" />
                  <span>Load Game</span>
                </button>
                
                <button
                  onClick={handleNewGame}
                  className="w-full flex items-center space-x-2 px-3 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
                >
                  <RotateCcw className="w-4 h-4" />
                  <span>New Game</span>
                </button>
              </div>
            )}
          </div>
          {/* Current Location */}
          <div className="bg-slate-700 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <MapPin className="w-4 h-4 text-purple-400" />
              <h3 className="font-semibold text-white">Current Location</h3>
            </div>
            <p className="text-gray-300 text-sm">{gameState.currentLocation}</p>
          </div>

          {/* Player Status */}
          <div className="bg-slate-700 rounded-lg p-4">
            <h3 className="font-semibold text-white mb-3">Player Status</h3>
            <div className="space-y-3">
              {/* Health */}
              <div>
                <div className="flex items-center justify-between mb-1">
                  <div className="flex items-center space-x-1">
                    <Heart className="w-4 h-4 text-red-400" />
                    <span className="text-sm text-gray-300">Health</span>
                  </div>
                  <span className="text-sm text-white">
                    {playerStats.health}/{playerStats.maxHealth}
                  </span>
                </div>
                <div className="w-full bg-slate-600 rounded-full h-2">
                  <div
                    className="bg-red-500 h-2 rounded-full transition-all duration-300"
                    style={{
                      width: `${(playerStats.health / playerStats.maxHealth) * 100}%`,
                    }}
                  />
                </div>
              </div>

              {/* Mana */}
              <div>
                <div className="flex items-center justify-between mb-1">
                  <div className="flex items-center space-x-1">
                    <Zap className="w-4 h-4 text-blue-400" />
                    <span className="text-sm text-gray-300">Mana</span>
                  </div>
                  <span className="text-sm text-white">
                    {playerStats.mana}/{playerStats.maxMana}
                  </span>
                </div>
                <div className="w-full bg-slate-600 rounded-full h-2">
                  <div
                    className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                    style={{
                      width: `${(playerStats.mana / playerStats.maxMana) * 100}%`,
                    }}
                  />
                </div>
              </div>

              {/* Level & Experience */}
              <div>
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm text-gray-300">Level {playerStats.level}</span>
                  <span className="text-sm text-white">
                    {playerStats.experience}/{playerStats.experienceToNext} XP
                  </span>
                </div>
                <div className="w-full bg-slate-600 rounded-full h-2">
                  <div
                    className="bg-yellow-500 h-2 rounded-full transition-all duration-300"
                    style={{
                      width: `${(playerStats.experience / playerStats.experienceToNext) * 100}%`,
                    }}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Current Scene */}
          <div className="bg-slate-700 rounded-lg p-4">
            <h3 className="font-semibold text-white mb-2">Scene Description</h3>
            <p className="text-gray-300 text-sm leading-relaxed">
              {gameState.currentScene}
            </p>
          </div>

          {/* Active Effects */}
          {gameState.activeEffects.length > 0 && (
            <div className="bg-slate-700 rounded-lg p-4">
              <h3 className="font-semibold text-white mb-2">Active Effects</h3>
              <div className="space-y-1">
                {gameState.activeEffects.map((effect, index) => (
                  <div
                    key={index}
                    className="text-sm text-yellow-400 bg-yellow-400/10 px-2 py-1 rounded"
                  >
                    {effect}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Quest Log */}
          {gameState.questLog.length > 0 && (
            <div className="bg-slate-700 rounded-lg p-4">
              <h3 className="font-semibold text-white mb-2">Active Quests</h3>
              <div className="space-y-2">
                {gameState.questLog.map((quest) => (
                  <div key={quest.id} className="text-sm">
                    <div className="text-white font-medium">{quest.name}</div>
                    <div className="text-gray-400 text-xs">{quest.description}</div>
                    <div className="w-full bg-slate-600 rounded-full h-1 mt-1">
                      <div
                        className="bg-green-500 h-1 rounded-full"
                        style={{ width: `${quest.progress}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Save Dialog */}
      {showSaveDialog && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-slate-800 rounded-lg p-6 w-96 max-w-[90vw]">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">Save Game</h3>
              <button
                onClick={() => setShowSaveDialog(false)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Save Name
                </label>
                <input
                  type="text"
                  value={saveSlotName}
                  onChange={(e) => setSaveSlotName(e.target.value)}
                  placeholder="Enter save name..."
                  className="w-full px-3 py-2 bg-slate-700 border border-purple-500/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                  onKeyPress={(e) => e.key === 'Enter' && handleSaveToSlot()}
                  autoFocus
                />
              </div>
              
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowSaveDialog(false)}
                  className="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSaveToSlot}
                  className="flex-1 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                >
                  Save
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Load Dialog */}
      {showLoadDialog && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-slate-800 rounded-lg p-6 w-96 max-w-[90vw] max-h-[80vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">Load Game</h3>
              <button
                onClick={() => setShowLoadDialog(false)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            
            <div className="space-y-3">
              {savedGames.length === 0 ? (
                <div className="text-center text-gray-400 py-8">
                  <p>No saved games found</p>
                </div>
              ) : (
                savedGames.map((saveName) => {
                  const saveData = localStorage.getItem(`ai-dungeon-save-${saveName}`);
                  let timestamp = 'Unknown';
                  
                  try {
                    if (saveData) {
                      const parsed = JSON.parse(saveData);
                      if (parsed.timestamp) {
                        timestamp = new Date(parsed.timestamp).toLocaleString();
                      }
                    }
                  } catch (error) {
                    // Ignore parsing errors
                  }
                  
                  return (
                    <div
                      key={saveName}
                      className="flex items-center justify-between p-3 bg-slate-700 rounded-lg hover:bg-slate-600 transition-colors"
                    >
                      <div className="flex-1">
                        <div className="text-white font-medium">{saveName}</div>
                        <div className="text-xs text-gray-400">{timestamp}</div>
                      </div>
                      
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleLoadFromSlot(saveName)}
                          className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded transition-colors"
                        >
                          Load
                        </button>
                        <button
                          onClick={() => handleDeleteSave(saveName)}
                          className="px-2 py-1 bg-red-600 hover:bg-red-700 text-white text-sm rounded transition-colors"
                        >
                          <Trash2 className="w-3 h-3" />
                        </button>
                      </div>
                    </div>
                  );
                })
              )}
            </div>
            
            <div className="mt-4">
              <button
                onClick={() => setShowLoadDialog(false)}
                className="w-full px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatInterface;
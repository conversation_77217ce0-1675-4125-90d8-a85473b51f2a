import { useState } from 'react';
import { Sword, Shield, Gem, Package, Trash2, Eye } from 'lucide-react';
import { useGameStore } from '@/lib/store';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import type { Item } from '@/lib/schemas';

const Inventory = () => {
  const {
    inventory,
    equipment,
    playerStats,
    equipItem,
    unequipItem,
    removeItem,
  } = useGameStore();

  const [selectedItem, setSelectedItem] = useState<Item | null>(null);

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common':
        return 'border-gray-400 bg-gray-400/10';
      case 'uncommon':
        return 'border-green-400 bg-green-400/10';
      case 'rare':
        return 'border-blue-400 bg-blue-400/10';
      case 'epic':
        return 'border-purple-400 bg-purple-400/10';
      case 'legendary':
        return 'border-yellow-400 bg-yellow-400/10';
      default:
        return 'border-gray-400 bg-gray-400/10';
    }
  };

  const getItemIcon = (type: string) => {
    switch (type) {
      case 'weapon':
        return Sword;
      case 'armor':
        return Shield;
      case 'accessory':
        return Gem;
      default:
        return Package;
    }
  };



  const calculateTotalStats = () => {
    const baseStats = {
      attack: 0,
      defense: 0,
      health: 0,
      mana: 0,
      strength: 0,
      dexterity: 0,
      intelligence: 0,
      constitution: 0,
    };

    Object.values(equipment).forEach(item => {
      if (item?.stats) {
        Object.entries(item.stats).forEach(([stat, value]) => {
          if (value && stat in baseStats) {
            baseStats[stat as keyof typeof baseStats] += value;
          }
        });
      }
    });

    return baseStats;
  };

  const totalStats = calculateTotalStats();

  return (
    <div className="h-screen bg-slate-900 p-6 overflow-y-auto">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Equipment Slots */}
          <div className="lg:col-span-1">
            <div className="bg-slate-800 rounded-lg p-6 border border-purple-500/20">
              <h2 className="text-xl font-bold text-white mb-4">Equipment</h2>
              
              {/* Character Silhouette */}
              <div className="relative bg-slate-700 rounded-lg p-4 mb-4">
                <div className="space-y-4">
                  {/* Weapon Slot */}
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-slate-600 rounded-lg border-2 border-dashed border-gray-500 flex items-center justify-center">
                      {equipment.weapon ? (
                        <Sword className="w-6 h-6 text-yellow-400" />
                      ) : (
                        <Sword className="w-6 h-6 text-gray-500" />
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="text-sm text-gray-400">Weapon</div>
                      <div className="text-white font-medium">
                        {equipment.weapon?.name || 'None'}
                      </div>
                    </div>
                  </div>

                  {/* Armor Slot */}
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-slate-600 rounded-lg border-2 border-dashed border-gray-500 flex items-center justify-center">
                      {equipment.armor ? (
                        <Shield className="w-6 h-6 text-blue-400" />
                      ) : (
                        <Shield className="w-6 h-6 text-gray-500" />
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="text-sm text-gray-400">Armor</div>
                      <div className="text-white font-medium">
                        {equipment.armor?.name || 'None'}
                      </div>
                    </div>
                  </div>

                  {/* Accessory Slot */}
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-slate-600 rounded-lg border-2 border-dashed border-gray-500 flex items-center justify-center">
                      {equipment.accessory ? (
                        <Gem className="w-6 h-6 text-purple-400" />
                      ) : (
                        <Gem className="w-6 h-6 text-gray-500" />
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="text-sm text-gray-400">Accessory</div>
                      <div className="text-white font-medium">
                        {equipment.accessory?.name || 'None'}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Equipment Stats */}
              <div className="bg-slate-700 rounded-lg p-4">
                <h3 className="text-white font-semibold mb-3">Equipment Bonuses</h3>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  {Object.entries(totalStats).map(([stat, value]) => (
                    <div key={stat} className="flex justify-between">
                      <span className="text-gray-400 capitalize">{stat}:</span>
                      <span className={cn(
                        'font-medium',
                        value > 0 ? 'text-green-400' : 'text-gray-500'
                      )}>
                        {value > 0 ? `+${value}` : '0'}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Inventory Grid */}
          <div className="lg:col-span-2">
            <div className="bg-slate-800 rounded-lg p-6 border border-purple-500/20">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-bold text-white">Inventory</h2>
                <div className="text-sm text-gray-400">
                  {inventory.length} items
                </div>
              </div>

              {/* Inventory Grid */}
              <div className="grid grid-cols-6 md:grid-cols-8 gap-2 mb-6">
                {Array.from({ length: 48 }, (_, index) => {
                  const item = inventory[index];
                  const ItemIcon = item ? getItemIcon(item.type) : Package;
                  
                  return (
                    <div
                      key={index}
                      className={cn(
                        'aspect-square border-2 rounded-lg p-2 cursor-pointer transition-all duration-200',
                        'hover:border-purple-400 focus:outline-none focus:ring-2 focus:ring-purple-500',
                        item
                          ? `${getRarityColor(item.rarity)} hover:scale-105`
                          : 'border-slate-600 bg-slate-700'
                      )}
                      onClick={() => item && setSelectedItem(item)}
                      title={item ? `${item.name} (${item.quantity})` : 'Empty slot'}
                    >
                      {item && (
                        <div className="w-full h-full flex flex-col items-center justify-center">
                          <ItemIcon className="w-6 h-6 text-white mb-1" />
                          {item.quantity > 1 && (
                            <span className="text-xs text-white bg-black/50 rounded px-1">
                              {item.quantity}
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>


            </div>
          </div>
        </div>

        {/* Item Details Modal */}
        {selectedItem && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
            <div className="bg-slate-800 rounded-lg p-6 max-w-md w-full border border-purple-500/20">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-bold text-white">{selectedItem.name}</h3>
                <button
                  onClick={() => setSelectedItem(null)}
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  ×
                </button>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <span className={cn(
                    'px-2 py-1 rounded text-xs font-medium',
                    getRarityColor(selectedItem.rarity)
                  )}>
                    {selectedItem.rarity.toUpperCase()}
                  </span>
                  <span className="text-gray-400 text-sm">{selectedItem.type}</span>
                  {selectedItem.quantity > 1 && (
                    <span className="text-white text-sm">×{selectedItem.quantity}</span>
                  )}
                </div>
                
                <p className="text-gray-300 text-sm leading-relaxed">
                  {selectedItem.description}
                </p>
                
                {selectedItem.stats && Object.keys(selectedItem.stats).length > 0 && (
                  <div className="bg-slate-700 rounded-lg p-3">
                    <h4 className="text-white font-medium mb-2">Stats</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      {Object.entries(selectedItem.stats).map(([stat, value]) => (
                        value && (
                          <div key={stat} className="flex justify-between">
                            <span className="text-gray-400 capitalize">{stat}:</span>
                            <span className="text-green-400 font-medium">+{value}</span>
                          </div>
                        )
                      ))}
                    </div>
                  </div>
                )}
                
                <div className="flex space-x-2">
                  {selectedItem.equipSlot && !selectedItem.equipped && (
                    <button
                      onClick={() => {
                        equipItem(selectedItem.id);
                        setSelectedItem(null);
                        toast.success(`Equipped ${selectedItem.name}!`);
                      }}
                      className="flex-1 px-3 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
                    >
                      Equip
                    </button>
                  )}
                  <button
                    onClick={() => {
                      removeItem(selectedItem.id, 1);
                      setSelectedItem(null);
                      toast.success(`Dropped ${selectedItem.name}.`);
                    }}
                    className="flex-1 px-3 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors flex items-center justify-center space-x-1"
                  >
                    <Trash2 className="w-4 h-4" />
                    <span>Drop</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Inventory;
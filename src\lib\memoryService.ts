import type {
  MemoryEntry,
  Character,
  CharacterConsistency,
  LongTermMemory,
} from './schemas';

/**
 * Sophisticated Long-term Memory Service
 * Handles character consistency, semantic search, and memory management
 */
class MemoryService {
  private memoryIndex: Map<string, MemoryEntry> = new Map();
  private characterConsistency: Map<string, CharacterConsistency> = new Map();
  private longTermMemory: LongTermMemory = {
    characterMemories: {},
    locationMemories: {},
    plotMemories: [],
    relationshipMemories: {},
    semanticIndex: {},
    memoryGraph: {},
    importantMoments: [],
  };

  /**
   * Add a new memory entry with automatic categorization and indexing
   */
  addMemory(memory: Omit<MemoryEntry, 'id' | 'timestamp' | 'accessCount' | 'lastAccessed'>): string {
    const memoryId = `mem_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const fullMemory: MemoryEntry = {
      ...memory,
      id: memoryId,
      timestamp: new Date(),
      accessCount: 0,
      lastAccessed: undefined,
    };

    this.memoryIndex.set(memoryId, fullMemory);
    this.indexMemory(fullMemory);
    this.updateSemanticIndex(fullMemory);
    
    return memoryId;
  }

  /**
   * Retrieve memories with sophisticated filtering and ranking
   */
  getMemories(options: {
    characterId?: string;
    location?: string;
    category?: string;
    importance?: number;
    limit?: number;
    keywords?: string[];
    timeRange?: { start: Date; end: Date };
  }): MemoryEntry[] {
    let memories = Array.from(this.memoryIndex.values());

    // Apply filters
    if (options.characterId) {
      memories = memories.filter(m => m.relatedCharacters.includes(options.characterId!));
    }
    if (options.location) {
      memories = memories.filter(m => m.location === options.location);
    }
    if (options.category) {
      memories = memories.filter(m => m.category === options.category);
    }
    if (options.importance) {
      memories = memories.filter(m => m.importance >= options.importance!);
    }
    if (options.timeRange) {
      memories = memories.filter(m => 
        m.timestamp >= options.timeRange!.start && 
        m.timestamp <= options.timeRange!.end
      );
    }
    if (options.keywords) {
      memories = memories.filter(m => 
        options.keywords!.some(keyword => 
          m.keywords?.includes(keyword) || 
          m.content.toLowerCase().includes(keyword.toLowerCase()) ||
          m.tags.some(tag => tag.toLowerCase().includes(keyword.toLowerCase()))
        )
      );
    }

    // Sort by importance and recency
    memories.sort((a, b) => {
      const importanceWeight = 0.7;
      const recencyWeight = 0.3;
      
      const aScore = (a.importance * importanceWeight) + 
                    (this.getRecencyScore(a.timestamp) * recencyWeight);
      const bScore = (b.importance * importanceWeight) + 
                    (this.getRecencyScore(b.timestamp) * recencyWeight);
      
      return bScore - aScore;
    });

    // Update access tracking
    memories.forEach(memory => {
      memory.accessCount++;
      memory.lastAccessed = new Date();
    });

    return memories.slice(0, options.limit || 50);
  }

  /**
   * Get character-defining memories for consistency checking
   */
  getCharacterDefiningMemories(characterId: string): MemoryEntry[] {
    return this.getMemories({
      characterId,
      limit: 20
    }).filter(m => m.isCharacterDefining || m.category === 'character_introduction');
  }

  /**
   * Validate character consistency and detect contradictions
   */
  validateCharacterConsistency(character: Character, newMemory: MemoryEntry): {
    isConsistent: boolean;
    contradictions: string[];
    suggestions: string[];
  } {
    const characterId = character.id;
    const existingMemories = this.getCharacterDefiningMemories(characterId);
    const contradictions: string[] = [];
    const suggestions: string[] = [];

    // Check for physical description consistency
    if (character.physicalDescription && newMemory.content) {
      const existingDescriptions = existingMemories
        .filter(m => m.category === 'character_introduction')
        .map(m => m.content);
      
      if (existingDescriptions.length > 0) {
        const hasConflict = this.detectDescriptionConflict(
          existingDescriptions,
          newMemory.content
        );
        if (hasConflict) {
          contradictions.push(`Physical description conflict detected for ${character.name}`);
          suggestions.push(`Review previous descriptions: ${existingDescriptions[0].substring(0, 100)}...`);
        }
      }
    }

    // Check personality consistency
    if (character.personality && newMemory.category === 'character_interaction') {
      const personalityMemories = existingMemories.filter(m => 
        m.content.toLowerCase().includes('personality') ||
        m.content.toLowerCase().includes('character') ||
        m.tags.includes('personality')
      );
      
      if (personalityMemories.length > 0) {
        const hasPersonalityConflict = this.detectPersonalityConflict(
          character.personality,
          newMemory.content
        );
        if (hasPersonalityConflict) {
          contradictions.push(`Personality inconsistency detected for ${character.name}`);
          suggestions.push(`Established personality: ${character.personality}`);
        }
      }
    }

    // Update consistency tracker
    this.updateCharacterConsistency(characterId, newMemory, contradictions.length === 0);

    return {
      isConsistent: contradictions.length === 0,
      contradictions,
      suggestions
    };
  }

  /**
   * Get contextual memories for AI prompt building
   */
  getContextualMemories(options: {
    characterIds: string[];
    currentLocation: string;
    recentMessages: any[];
    maxMemories?: number;
  }): MemoryEntry[] {
    const { characterIds, currentLocation, recentMessages, maxMemories = 30 } = options;
    
    // Extract keywords from recent messages
    const keywords = this.extractKeywords(recentMessages.map(m => m.content).join(' '));
    
    // Get memories for active characters
    const characterMemories = characterIds.flatMap(id => 
      this.getMemories({ characterId: id, limit: 10 })
    );
    
    // Get location-specific memories
    const locationMemories = this.getMemories({ 
      location: currentLocation, 
      limit: 10 
    });
    
    // Get keyword-relevant memories
    const keywordMemories = this.getMemories({ 
      keywords, 
      limit: 10 
    });
    
    // Get important plot memories
    const plotMemories = this.longTermMemory.plotMemories
      .map(id => this.memoryIndex.get(id))
      .filter(Boolean)
      .slice(0, 5) as MemoryEntry[];
    
    // Combine and deduplicate
    const allMemories = [...characterMemories, ...locationMemories, ...keywordMemories, ...plotMemories];
    const uniqueMemories = Array.from(
      new Map(allMemories.map(m => [m.id, m])).values()
    );
    
    // Sort by relevance and importance
    uniqueMemories.sort((a, b) => {
      const aRelevance = this.calculateRelevanceScore(a, keywords, characterIds, currentLocation);
      const bRelevance = this.calculateRelevanceScore(b, keywords, characterIds, currentLocation);
      return bRelevance - aRelevance;
    });
    
    return uniqueMemories.slice(0, maxMemories);
  }

  /**
   * Build comprehensive character context for AI
   */
  buildCharacterContext(characterId: string): string {
    const memories = this.getCharacterDefiningMemories(characterId);
    const consistency = this.characterConsistency.get(characterId);
    
    let context = '';
    
    if (memories.length > 0) {
      context += `\nCHARACTER MEMORY CONTEXT:\n`;
      memories.forEach(memory => {
        context += `- ${memory.content}\n`;
      });
    }
    
    if (consistency) {
      context += `\nESTABLISHED TRAITS:\n`;
      Object.entries(consistency.establishedTraits).forEach(([trait, data]) => {
        context += `- ${trait}: ${data.value} (confidence: ${Math.round(data.confidence * 100)}%)\n`;
      });
    }
    
    return context;
  }

  /**
   * Private helper methods
   */
  private indexMemory(memory: MemoryEntry): void {
    // Index by characters
    memory.relatedCharacters.forEach(characterId => {
      if (!this.longTermMemory.characterMemories[characterId]) {
        this.longTermMemory.characterMemories[characterId] = [];
      }
      this.longTermMemory.characterMemories[characterId].push(memory.id);
    });

    // Index by location
    if (memory.location) {
      if (!this.longTermMemory.locationMemories[memory.location]) {
        this.longTermMemory.locationMemories[memory.location] = [];
      }
      this.longTermMemory.locationMemories[memory.location].push(memory.id);
    }

    // Index plot memories
    if (memory.category === 'plot_event' && memory.importance >= 7) {
      this.longTermMemory.plotMemories.push(memory.id);
    }

    // Index important moments
    if (memory.importance >= 8) {
      this.longTermMemory.importantMoments.push({
        memoryId: memory.id,
        significance: memory.importance,
        impact: memory.summary || memory.content.substring(0, 100),
      });
    }
  }

  private updateSemanticIndex(memory: MemoryEntry): void {
    const keywords = memory.keywords || this.extractKeywords(memory.content);
    keywords.forEach(keyword => {
      if (!this.longTermMemory.semanticIndex[keyword]) {
        this.longTermMemory.semanticIndex[keyword] = [];
      }
      this.longTermMemory.semanticIndex[keyword].push(memory.id);
    });
  }

  private extractKeywords(text: string): string[] {
    // Simple keyword extraction - could be enhanced with NLP
    const words = text.toLowerCase()
      .replace(/[^a-zA-Z0-9\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 3);
    
    // Remove common words
    const stopWords = new Set(['this', 'that', 'with', 'have', 'will', 'from', 'they', 'been', 'said', 'each', 'which', 'their', 'time', 'would', 'there', 'could', 'other']);
    
    return [...new Set(words.filter(word => !stopWords.has(word)))];
  }

  private getRecencyScore(timestamp: Date): number {
    const now = new Date();
    const diffHours = (now.getTime() - timestamp.getTime()) / (1000 * 60 * 60);
    return Math.max(0, 1 - (diffHours / (24 * 7))); // Decay over a week
  }

  private detectDescriptionConflict(existing: string[], newContent: string): boolean {
    // Simple conflict detection - could be enhanced with NLP
    const physicalTerms = ['hair', 'eyes', 'tall', 'short', 'young', 'old', 'beard', 'scar'];
    
    for (const term of physicalTerms) {
      const existingMentions = existing.filter(desc => desc.toLowerCase().includes(term));
      const newMentions = newContent.toLowerCase().includes(term);
      
      if (existingMentions.length > 0 && newMentions) {
        // More sophisticated conflict detection would go here
        return false; // Simplified for now
      }
    }
    
    return false;
  }

  private detectPersonalityConflict(establishedPersonality: string, newContent: string): boolean {
    // Simple personality conflict detection
    const personalityTerms = ['kind', 'cruel', 'gentle', 'harsh', 'friendly', 'hostile', 'calm', 'angry'];
    
    // This is a simplified implementation
    return false;
  }

  private updateCharacterConsistency(characterId: string, memory: MemoryEntry, isConsistent: boolean): void {
    if (!this.characterConsistency.has(characterId)) {
      this.characterConsistency.set(characterId, {
        characterId,
        establishedTraits: {},
        consistencyScore: 1.0,
        lastValidated: new Date(),
      });
    }

    const consistency = this.characterConsistency.get(characterId)!;
    
    // Update consistency score
    if (isConsistent) {
      consistency.consistencyScore = Math.min(1.0, consistency.consistencyScore + 0.01);
    } else {
      consistency.consistencyScore = Math.max(0.0, consistency.consistencyScore - 0.1);
    }
    
    consistency.lastValidated = new Date();
  }

  private calculateRelevanceScore(
    memory: MemoryEntry, 
    keywords: string[], 
    characterIds: string[], 
    currentLocation: string
  ): number {
    let score = memory.importance;
    
    // Boost for character relevance
    if (memory.relatedCharacters.some(id => characterIds.includes(id))) {
      score += 3;
    }
    
    // Boost for location relevance
    if (memory.location === currentLocation) {
      score += 2;
    }
    
    // Boost for keyword matches
    const keywordMatches = keywords.filter(keyword => 
      memory.keywords?.includes(keyword) || 
      memory.content.toLowerCase().includes(keyword)
    ).length;
    score += keywordMatches;
    
    // Boost for recent access
    if (memory.lastAccessed) {
      const hoursSinceAccess = (new Date().getTime() - memory.lastAccessed.getTime()) / (1000 * 60 * 60);
      if (hoursSinceAccess < 24) {
        score += 1;
      }
    }
    
    return score;
  }

  /**
   * Export memory data for persistence
   */
  exportMemoryData(): {
    memories: MemoryEntry[];
    consistency: CharacterConsistency[];
    longTermMemory: LongTermMemory;
  } {
    return {
      memories: Array.from(this.memoryIndex.values()),
      consistency: Array.from(this.characterConsistency.values()),
      longTermMemory: this.longTermMemory,
    };
  }

  /**
   * Import memory data from persistence
   */
  importMemoryData(data: {
    memories: MemoryEntry[];
    consistency: CharacterConsistency[];
    longTermMemory: LongTermMemory;
  }): void {
    this.memoryIndex.clear();
    this.characterConsistency.clear();
    
    data.memories.forEach(memory => {
      this.memoryIndex.set(memory.id, memory);
    });
    
    data.consistency.forEach(consistency => {
      this.characterConsistency.set(consistency.characterId, consistency);
    });
    
    this.longTermMemory = data.longTermMemory;
  }
}

export const memoryService = new MemoryService();
export default memoryService;